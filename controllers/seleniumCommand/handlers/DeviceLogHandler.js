'use strict';

const bridge = require('../../../bridge');
const requestlib = require('../../../lib/request');
const HubLogger = require('../../../log');
const constants = require('../../../constants');

const LL = constants.LOG_LEVEL;

class DeviceLogHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  static validateRequestData(data) {
    const errors = [];
    if (!Object.prototype.hasOwnProperty.call(data, 'type')) {
      errors.push('Request data must contain a \'type\' key.');
    } else {
      const logType = data.type;
      if (!['logcat', 'syslog'].includes(logType)) {
        errors.push('Log type must be either \'logcat\' or \'syslog\'.');
      }
    }
    return errors.length > 0 ? errors : null;
  }

  async processCommand(requestStateObj, data) {
    HubLogger.miscLogger('DeviceLogHandler', `Processing device log request for session: ${this.sessionKeyObj.rails_session_id} device: ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);

    const latestSessionObj = constants.global_registry[this.sessionKeyObj.rails_session_id];
    if (latestSessionObj) {
      // Update our reference to use the latest session object
      this.sessionKeyObj = latestSessionObj;
    }

    try {
      // Parse the request data to get log type
      let logData;
      try {
        logData = JSON.parse(data);
      } catch (e) {
        throw new Error('Invalid JSON in request data');
      }

      // Validate request data
      const validationErrors = DeviceLogHandler.validateRequestData(logData);
      if (validationErrors) {
        throw new Error(validationErrors.join(', '));
      }
      // Check if device logs are enabled for this session
      const deviceLogsEnabled = this.sessionKeyObj.deviceLogs === true || this.sessionKeyObj.deviceLogs === 'true';
      if (!deviceLogsEnabled) {
        const errorMessage = 'Device logs must be enabled for this session';
        HubLogger.miscLogger('DeviceLogHandler', `Device logs not enabled for session: ${this.sessionKeyObj.rails_session_id}. deviceLogs property: ${this.sessionKeyObj.deviceLogs}`, LL.WARN, this.sessionKeyObj.debugSession);
        requestStateObj.data = JSON.stringify({
          sessionId: this.sessionKeyObj.rails_session_id,
          status: 13,
          value: { message: errorMessage }
        });
        requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 400 };
        bridge.sendResponse(this.sessionKeyObj, requestStateObj);
        return;
      }

      // Get the last end position for pagination (stored in session)
      const lastEndPos = this.sessionKeyObj.deviceLogEndPos || 0;

      // Call the platform endpoint
      const customTimeout = (this.sessionKeyObj.idle_timeout - (this.sessionKeyObj.idle_timeout > 20 ? 20 : 0)) * 1000;
      const serverURL = `/device_logs?device=${encodeURIComponent(this.sessionKeyObj.device)}&session_id=${encodeURIComponent(this.sessionKeyObj.rails_session_id)}&log_type=${encodeURIComponent(logData.type)}&start_pos=${lastEndPos}`;

      const termOptions = {
        hostname: this.sessionKeyObj.rproxyHost,
        port: 45671,
        path: serverURL,
        timeout: customTimeout,
        headers: requestlib.appendBStackHostHeader(this.sessionKeyObj.name),
      };

      HubLogger.miscLogger('DeviceLogHandler', `Making request to platform: ${serverURL}`, LL.DEBUG, this.sessionKeyObj.debugSession);
      const platformResponse = await requestlib.call(termOptions);

      if (platformResponse.statusCode === 200) {
        let responseData;
        try {
          responseData = JSON.parse(platformResponse.data);
        } catch (e) {
          throw new Error('Invalid JSON response from platform');
        }

        // Store the end_pos for next request
        if (responseData.meta && responseData.meta.end_pos) {
          const newEndPos = responseData.meta.end_pos;
          const oldEndPos = this.sessionKeyObj.deviceLogEndPos || 0;

          HubLogger.miscLogger('DeviceLogHandler', `Pagination - Received new end_pos from platform: ${newEndPos} (previous: ${oldEndPos}) for session: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG, this.sessionKeyObj.debugSession);

          this.sessionKeyObj.deviceLogEndPos = newEndPos;

          // Update the session object in memory
          if (constants.global_registry[this.sessionKeyObj.rails_session_id]) {
            constants.global_registry[this.sessionKeyObj.rails_session_id].deviceLogEndPos = newEndPos;
          }
        }

        // Remove meta object and return only the value array
        const logEntries = responseData.value || [];

        requestStateObj.data = JSON.stringify({
          sessionId: this.sessionKeyObj.rails_session_id,
          status: 0,
          value: logEntries
        });
        bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      } else {
        const errorMessage = '[BROWSERSTACK_INTERNAL_ERROR] We couldn\'t retrieve device logs. Please try again.';
        requestStateObj.data = JSON.stringify({
          sessionId: this.sessionKeyObj.rails_session_id,
          status: 13,
          value: { message: errorMessage }
        });
        requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
        bridge.sendResponse(this.sessionKeyObj, requestStateObj);
      }
    } catch (err) {
      HubLogger.exceptionLogger('Error in DeviceLogHandler', err);
      const errorMessage = '[BROWSERSTACK_INTERNAL_ERROR] We couldn\'t retrieve device logs. Please try again.';
      requestStateObj.data = JSON.stringify({
        sessionId: this.sessionKeyObj.rails_session_id,
        status: 13,
        value: { message: errorMessage }
      });
      requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode: 500 };
      bridge.sendResponse(this.sessionKeyObj, requestStateObj);
    }
  }
}

module.exports = { DeviceLogHandler };
