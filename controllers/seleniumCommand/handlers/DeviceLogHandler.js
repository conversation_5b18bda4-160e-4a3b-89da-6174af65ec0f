'use strict';

const bridge = require('../../../bridge');
const requestlib = require('../../../lib/request');
const HubLogger = require('../../../log');
const constants = require('../../../constants');

const LL = constants.LOG_LEVEL;

class DeviceLogHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  static validateRequestData(data) {
    const errors = [];
    if (!Object.prototype.hasOwnProperty.call(data, 'type')) {
      errors.push('Request data must contain a \'type\' key.');
    } else {
      const logType = data.type;
      if (!['logcat', 'syslog'].includes(logType)) {
        errors.push('Log type must be either \'logcat\' or \'syslog\'.');
      }
    }
    return errors.length > 0 ? errors : null;
  }

  parseRequestData(data) {
    try {
      return JSON.parse(data);
    } catch (e) {
      throw new Error('Invalid JSON in request data');
    }
  }

  validateDeviceLogsEnabled() {
    const deviceLogsEnabled = this.sessionKeyObj.deviceLogs === true || this.sessionKeyObj.deviceLogs === 'true';
    if (!deviceLogsEnabled) {
      const errorMessage = 'Device logs must be enabled for this session';
      HubLogger.miscLogger('DeviceLogHandler', `Device logs not enabled for session: ${this.sessionKeyObj.rails_session_id}. deviceLogs property: ${this.sessionKeyObj.deviceLogs}`, LL.WARN, this.sessionKeyObj.debugSession);
      throw new Error(errorMessage);
    }
  }

  preparePlatformRequest(logData, lastEndPos) {
    const idleTimeout = this.sessionKeyObj.idle_timeout;
    const timeoutOffset = idleTimeout > 20 ? 20 : 0;
    const customTimeout = (idleTimeout - timeoutOffset) * 1000;
    const serverURL = `/device_logs?device=${encodeURIComponent(this.sessionKeyObj.device)}`
      + `&session_id=${encodeURIComponent(this.sessionKeyObj.rails_session_id)}`
      + `&log_type=${encodeURIComponent(logData.type)}&start_pos=${lastEndPos}`;

    return {
      hostname: this.sessionKeyObj.rproxyHost,
      port: 45671,
      path: serverURL,
      timeout: customTimeout,
      headers: requestlib.appendBStackHostHeader(this.sessionKeyObj.name),
    };
  }

  updatePaginationState(responseData) {
    if (responseData.meta && responseData.meta.end_pos) {
      const newEndPos = responseData.meta.end_pos;
      const oldEndPos = this.sessionKeyObj.deviceLogEndPos || 0;

      HubLogger.miscLogger('DeviceLogHandler', `Pagination - Received new end_pos from platform: ${newEndPos} (previous: ${oldEndPos}) for session: ${this.sessionKeyObj.rails_session_id}`, LL.DEBUG, this.sessionKeyObj.debugSession);

      this.sessionKeyObj.deviceLogEndPos = newEndPos;

      // Update the session object in memory
      if (constants.global_registry[this.sessionKeyObj.rails_session_id]) {
        constants.global_registry[this.sessionKeyObj.rails_session_id].deviceLogEndPos = newEndPos;
      }
    }
  }

  createErrorResponse(requestStateObj, message, statusCode = 500) {
    requestStateObj.data = JSON.stringify({
      sessionId: this.sessionKeyObj.rails_session_id,
      status: 13,
      value: { message }
    });
    requestStateObj.output = { headers: constants.CHUNKED_HEADER, statusCode };
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }

  createSuccessResponse(requestStateObj, logEntries) {
    requestStateObj.data = JSON.stringify({
      sessionId: this.sessionKeyObj.rails_session_id,
      status: 0,
      value: logEntries
    });
    bridge.sendResponse(this.sessionKeyObj, requestStateObj);
  }

  async handlePlatformResponse(platformResponse, requestStateObj) {
    if (platformResponse.statusCode !== 200) {
      const errorMessage = '[BROWSERSTACK_INTERNAL_ERROR] We couldn\'t retrieve device logs. Please try again.';
      this.createErrorResponse(requestStateObj, errorMessage);
      return;
    }

    let responseData;
    try {
      responseData = JSON.parse(platformResponse.data);
    } catch (e) {
      throw new Error('Invalid JSON response from platform');
    }

    this.updatePaginationState(responseData);

    // Remove meta object and return only the value array
    const logEntries = responseData.value || [];
    this.createSuccessResponse(requestStateObj, logEntries);
  }

  async processCommand(requestStateObj, data) {
    HubLogger.miscLogger('DeviceLogHandler', `Processing device log request for session: ${this.sessionKeyObj.rails_session_id} device: ${this.sessionKeyObj.device}`, LL.DEBUG, this.sessionKeyObj.debugSession);

    const latestSessionObj = constants.global_registry[this.sessionKeyObj.rails_session_id];
    if (latestSessionObj) {
      // Update our reference to use the latest session object
      this.sessionKeyObj = latestSessionObj;
    }

    try {
      // Parse and validate request data
      const logData = this.parseRequestData(data);
      const validationErrors = DeviceLogHandler.validateRequestData(logData);
      if (validationErrors) {
        throw new Error(validationErrors.join(', '));
      }

      // Validate device logs are enabled
      this.validateDeviceLogsEnabled();

      // Prepare and make platform request
      const lastEndPos = this.sessionKeyObj.deviceLogEndPos || 0;
      const termOptions = this.preparePlatformRequest(logData, lastEndPos);

      HubLogger.miscLogger('DeviceLogHandler', `Making request to platform: ${termOptions.path}`, LL.DEBUG, this.sessionKeyObj.debugSession);
      const platformResponse = await requestlib.call(termOptions);

      await this.handlePlatformResponse(platformResponse, requestStateObj);
    } catch (err) {
      HubLogger.exceptionLogger('Error in DeviceLogHandler', err);
      const errorMessage = err.message === 'Device logs must be enabled for this session'
        ? err.message
        : '[BROWSERSTACK_INTERNAL_ERROR] We couldn\'t retrieve device logs. Please try again.';
      const statusCode = err.message === 'Device logs must be enabled for this session' ? 400 : 500;
      this.createErrorResponse(requestStateObj, errorMessage, statusCode);
    }
  }
}

module.exports = { DeviceLogHandler };
