/*
 * This file will contains helper functions.
 * Place independent helper functions in this file
 */

'use strict';


const crypto = require('crypto');
const { Events, Client: EdsClient, DWH } = require('browserstack-dwh');
const stringHash = require('string-hash');

const udp = require('dgram');
const redisClient = require('./redisUtils').redisClient;
const redisClientSecond = require('./redisUtils').redisClientSecond;
const redisClientAi = require('./redisUtils').redisClientAi;
const keysMap = require('./keys_map.json');
const constants = require('./constants');
const HubLogger = require('./log');
const ha = require('./ha');
const browserstack = require('./browserstack');
const pubSub = require('./pubSub');
const queueHandler = require('./queueHandler');
const requestlib = require('./lib/request');
const AlertManager = require('./alertManager');
const jwpMap = require('./jwp_status_code_map.json');
const errorMessages = require('./errorMessages');
const SeleniumClient = require('./seleniumClient');
const { isString, isTrueString, isAndroid, isNotUndefined, isUndefined, isHash, getType } = require('./typeSanity');
const instrumentation = require('./helpers/instrumentation');
const { getInstrumentationData } = require('./helpers/customSeleniumHandling/instrumentExecutor');
const browserstackErrorUtil = require('./helpers/browserstackErrorBucketUtil');
const sessionManagerHelper = require('./services/session/sessionManagerHelper');
const Qig = require('./helpers/qig');
const urlModule = require('url');
const workerHubRailsPipelineManager = require('./railsRequests/workerRailsPipelineManager');
const { AICommandHandler } = require('./controllers/seleniumCommand/handlers/AICommandHandler');
const { REMOTE_DEBUGGER_PORT } = require('./config/socketConstants');

const alertManager = new AlertManager();
const LL = constants.LOG_LEVEL;
const isProd = constants.isProductionEnv;
const QUEUE_REQUEST_EXPONENTIAL_DELAY = constants.QUEUED_REQUEST_EXPONENTIAL_DELAY;

const consoleTimes = new Map();

const dwhClient = new EdsClient(constants.eds_server, constants.eds_port, constants.eds_key, undefined, constants.zombie_server, constants.zombie_port);

const kafkaConfig = constants.kafkaConfig;

const CDP_CONSOLE_API_METHOD = "Runtime.consoleAPICalled";
const CDP_LOG_METHOD = "Log.entryAdded";
const CONSOLE_METHOD_LIST = new Set([CDP_CONSOLE_API_METHOD, CDP_LOG_METHOD]);

exports.redisClient = redisClient;
exports.redisClientSecond = redisClientSecond;
exports.redisClientAi = redisClientAi;

const isAppleOs = (os) => {
  return os && constants.APPLE_OS.includes(os.toLowerCase());
};

exports.isAppleOs = isAppleOs;

exports.getRproxy = function(options){
  let tmpRproxy = options.rproxyHost;
  if(options.browserstackParams && constants.eliminateRproxyPrivateHubSubRegions && constants.PRIVATE_HUB_REGIONS.has(constants.region) && constants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING[constants.region]  == options.browserstackParams["browserstack.terminal_sub_region"] ){
    tmpRproxy = options.host_name;
  }
  return tmpRproxy;
};

exports.getStopStateFromUrl = function(url){
  switch(url) {
    case '/stop_ui':
      return constants.SESSIONS_STOP_STATE.STOP_UI;
    case '/stop_limit':
      return constants.SESSIONS_STOP_STATE.STOP_LIMIT;
    case '/stop_smd':
      return constants.SESSIONS_STOP_STATE.STOP_SMD;
    case '/stop_limit_aa_freemium':
      return constants.SESSIONS_STOP_STATE.STOP_LIMIT_AA_FREEMIUM;
    default:
      return null;
  }
};

exports.validateDeleteChromeOptionsEdgeChromium = (selVersion) => {
  try {
    return (exports.isVersionEqualOrGreater(constants.DELETE_CHROME_OPTIONS_FOR_EDGE_CHROMIUM_FOR_SELENIUM_VERSIONS, selVersion) || constants.DELETE_CHROME_OPTIONS_FOR_EDGE_CHROMIUM_FOR_SELENIUM_VERSIONS_EXTRA.includes(selVersion)) && !constants.DELETE_CHROME_OPTIONS_FOR_EDGE_CHROMIUM_FOR_SELENIUM_VERSIONS_IGNORE.includes(selVersion);
  } catch (error) {
    HubLogger.miscLogger('Hub', 'Error in validateDeleteChromeOptionsEdgeChromium', LL.ERROR);
  }
  return false;
};

exports.validateDeleteBrowserVersion = (selVersion) => {
  try {
    return (exports.isVersionEqualOrGreater(constants.DELETE_BROWSER_VERSION_FOR_SELENIUM_VERSIONS, selVersion) || constants.DELETE_BROWSER_VERSION_FOR_SELENIUM_VERSIONS_EXTRA.includes(selVersion)) && !constants.DELETE_BROWSER_VERSION_FOR_SELENIUM_VERSIONS_IGNORE.includes(selVersion);
  } catch (error) {
    HubLogger.miscLogger('Hub', 'Error in validateDeleteBrowserVersion', LL.ERROR);
  }
  return false;
};

exports.validateCdpSeleniumJar = (selVersion) => {
  try {
    return (exports.isVersionEqualOrGreater(constants.CDP_SELENIUM_VERSIONS, selVersion) || constants.CDP_SELENIUM_VERSIONS_EXTRA.includes(selVersion)) && !constants.CDP_SELENIUM_VERSIONS_IGNORE.includes(selVersion);
  } catch (error) {
    HubLogger.miscLogger('Hub', 'Error in validateCdpSeleniumJar', LL.ERROR);
  }
  return false;
};

exports.validateBidiSeleniumJar = (selVersion) => {
  try {
    return (exports.isVersionEqualOrGreater(constants.BIDI_SELENIUM_VERSIONS, selVersion) || constants.BIDI_SELENIUM_VERSIONS_EXTRA.includes(selVersion)) && !constants.BIDI_SELENIUM_VERSIONS_IGNORE.includes(selVersion);
  } catch (error) {
    HubLogger.miscLogger('Hub', 'Error in validateBidiSeleniumJar', LL.ERROR);
  }
  return false;
};

exports.validateAIHealingDetails = (healingDetails) => {
  if (typeof healingDetails !== 'object' || healingDetails === null) {
    return false;
  }

  const schema = {
    total_healing_enabled_request: 'number',
    total_healing_request: 'number',
    script_exec_error_count: 'number',
    pre_check_failure_count: 'number',
    healing_failure_count: 'number',
    healing_success_count: 'number',
  };

  for (const [key, type] of Object.entries(schema)) {
    if (!(key in healingDetails)) {
      return false;
    }

    const value = healingDetails[key];
    if (type === 'number' && typeof value !== 'number') {
      return false;
    }
  }

  return true;
};

exports.isVersionEqualOrGreater = function(threshold, verToCheck) {
  try {
    const v1Components = threshold.split('.').map(Number);
    const v2Components = verToCheck.split('.').map(Number);

    for (let i = 0; i < Math.max(v1Components.length, v2Components.length); i++) {
      const v1Component = v1Components[i] || 0;
      const v2Component = v2Components[i] || 0;

      if (v1Component < v2Component) {
        return true;
      } else if (v1Component > v2Component) {
        return false;
      }
    }
  } catch (error) {
    HubLogger.miscLogger('Hub', `Error in isVersionEqualOrGreater ${error.toString()}`, LL.ERROR);
    return false;
  }
  return true;
};

exports.validSDKVersionForDynamicHubAllocation = function(browserstackSDK){
  try {
    if (browserstackSDK && browserstackSDK.indexOf('/') > -1) {
      const split = browserstackSDK.split('/');
      const sdk = split[0];
      const agent = sdk.split('-').pop();
      const version = split[1];
      return exports.isVersionEqualOrGreater(constants.MIN_VER_SDK_AGENT_MAPPING[agent], version);
    }
  } catch (error) {
    HubLogger.miscLogger('Hub', 'Error in validSDKVersionForDynamicHubAllocation', LL.ERROR);
  }
  return false;
};

exports.endConflictResponse = function(response, statusCode=409){
  response.writeHead(statusCode, {'content-type': 'application/json; charset=utf-8'});
  return response.end();
};

exports.checkSessionAppAutomate = function (sessionId, callback) {
  if (isNotUndefined(constants.global_registry[sessionId])) {
    const keyObject = constants.global_registry[sessionId];
    callback(!!keyObject.appTesting);
  } else {
    callback(false);
  }
};

// returns true needs to push to kafka, false if needs to pubsub
exports.shouldSendInstrumentationThroughKafka = (key, updatedValue) => {
  if (!constants.pubSubKafkaKeys[key] || constants.pubSubKafkaKeys[key] === 'N') {
    return false;
  }

  // Write cases for keys where, constants.pubSubKafkaKeys[key] === 'C'
  // and in return, put condition such that it return true if needs to send through kafka
  switch (key) {
    case 'request_count':
      // send through kafka when request_count is > 2
      return updatedValue > 2;
    default:
      // For keys where, constants.pubSubKafkaKeys[key] value is 'Y' || 'M'
      return true;
  }
};

// This functions adds start/stop markers in appium session logs for letting us know extra
// commands executed by hub. Logs between start/stop markers are removed on platform before uploading to S3
exports.logAppiumBridgeCommand = function(keyObject, bridgeName, logType, retry) {
  return new Promise(function(resolve) {
    if (!isAppleOs(keyObject.os) || !keyObject.appiumLogs) {
      resolve();
      return;
    }
    retry = (typeof retry === "undefined" || retry) ? true : false;
    var logBridgeParams = {
      automate_session_id: keyObject.rails_session_id,
      device: keyObject.device,
      bridge_name: bridgeName,
      log_type: logType
    };

    var logBridgeUrl = '/log_bridge_commands?' + requestlib.getEncodedURLParams(logBridgeParams);
    var markerOptions = {
      method: 'GET',
      hostname: keyObject.rproxyHost,
      port: 45671,
      path: logBridgeUrl
    };
    markerOptions.headers = {
      "accept": "application/json",
      "content-type": "application/json; charset=utf-8",
      "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3"
    };
    requestlib.appendBStackHostHeader(keyObject.name, markerOptions.headers);
    var logString = ["session_id", keyObject.rails_session_id, "Logging bridge commands on platform for appium:", bridgeName, logType].join(" ");
    requestlib.call(markerOptions).then((res) => {
      HubLogger.miscLogger("Hub", logString + " " + res.statusCode.toString(), LL.INFO);
      if (res.statusCode.toString() !== "200" && retry) {
        HubLogger.miscLogger("Hub", "Non 200 response for logging bridge commands. Retrying.", LL.INFO);
        exports.logAppiumBridgeCommand(keyObject, bridgeName, logType, false)
        .then(function(){
          resolve();
        });
      } else {
        resolve();
      }
    }).catch((err) => {
      HubLogger.miscLogger('Error: ' + logString, err.toString(), LL.INFO);
      if (retry) {
        HubLogger.miscLogger("Hub", "Error for logging bridge commands. Retrying.", LL.INFO);
        exports.logAppiumBridgeCommand(keyObject, bridgeName, logType, false)
        .then(function(){
          resolve();
        });
      } else {
        resolve();
      }
    });
  });
};

exports.isHash = (entity) => Boolean(entity && typeof(entity) === "object" && !Array.isArray(entity));

exports.isTrue = (entity) => Boolean(entity && entity.toString().toLowerCase() === 'true');

exports.isDefined = (maybeDefinedObject) => {
  return typeof(maybeDefinedObject) !== 'undefined';
};

function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  let currentObject = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    currentObject[key] = currentObject[key] || {};
    currentObject = currentObject[key];
  }

  const lastKey = keys[keys.length - 1];
  currentObject[lastKey] = value;
}

exports.setNestedValue = setNestedValue;

exports.nestedKeyValue = (hash, keys) => keys.reduce((hash, key) => (exports.isHash(hash) && hash.hasOwnProperty(key) ? hash[key] : undefined), hash);

exports.getParsedObjectOrEmpty = function(toParseString, returnParsedObject = {}) {
  if(toParseString && typeof toParseString === 'string') {
    try {
      returnParsedObject = JSON.parse(toParseString);
    } catch(e) {
      //Ignore JSON Parsing errors/exceptions.
    }
  }
  return returnParsedObject;
};

exports.eliminateRproxyForWebsocketFrameworks = function (wsURLToChange, terminalIP, keyObject) {
  if (!keyObject || !terminalIP) {
    return wsURLToChange;
  }

  const featureFlag = constants.eliminateRproxyPrivateHubSubRegions;
  const currentHubRegionAllowedToSkipRproxy = constants.PRIVATE_HUB_REGIONS.has(constants.region);
  const currentHubRegionAllowedToSkipRproxyTerminal = constants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING[constants.region] === keyObject.terminalSubRegion;

  const doWeNeedToSkipRproxyForDualInterfaceHub = featureFlag && currentHubRegionAllowedToSkipRproxy && currentHubRegionAllowedToSkipRproxyTerminal;
  if (doWeNeedToSkipRproxyForDualInterfaceHub) {
    const url = new URL(wsURLToChange);
    url.hostname = terminalIP;
    url.port = REMOTE_DEBUGGER_PORT;
    return url.toString();
  }

  return wsURLToChange;
};

/**
 * Parses the String | Object | BufferArray Object of String and returns the Parsed Object
 * @param {String|Object} hashOrStringified
 */
exports.originalOrParsedObj = (hashOrStringified) => {
  if (isHash(hashOrStringified)) {
    if (hashOrStringified.toString() === '[object Object]') {
      return hashOrStringified;
    }
    return exports.getParsedObjectOrEmpty(hashOrStringified.toString());
  }

  return exports.getParsedObjectOrEmpty(hashOrStringified);
};

/**
 * Nested fields from a stringified|Object data type with the ability to perform
 * check on the expected result.
 * @param {String|Object} hashOrStringified
 * @param {Array<String>} keys
 * @param {} defaultReturnValue
 * @param {Function|String|Number|Boolean} testResultWith
 */
exports.nestedKeyValueGeneric = (hashOrStringified, keys, defaultReturnValue, testResultWith) => {
  const result = keys.reduce((hashOrStringified, key) => exports.originalOrParsedObj(hashOrStringified)[key], hashOrStringified);

  if ((typeof defaultReturnValue !== 'undefined') && (typeof testResultWith !== 'undefined')) {
    if (typeof testResultWith === 'function') {
      return testResultWith(result) ? result : defaultReturnValue;
    }
    return testResultWith === result ? result : defaultReturnValue;
  }

  return result;
};

exports.getHashOrEmpty = (hashObject) => {
  return exports.isHash(hashObject) ? hashObject : {};
};

exports.isNonEmptyHash = (hashObject) => {
  return (Object.keys(exports.getHashOrEmpty(hashObject)).length !== 0);
};

exports.getArrayOrEmpty = (arrObject) => {
  return Array.isArray(arrObject) ? arrObject : [];
};

exports.getCountOfKeys = (hashObject, current_count, limit) => {
  let new_keys = 0;
  if(!isHash(hashObject)) return 0;
  for(const currentKey of Object.keys(hashObject)) {
    // add current key
    new_keys += 1;
    // count child keys
    new_keys += exports.getCountOfKeys(hashObject[currentKey], current_count + new_keys, limit);
    // check total number of keys found till now and stop in case we hit limit
    if((current_count + new_keys) > limit) return new_keys;
  }
  return new_keys;
};

const logUDPFailure = function (error, _bytes) {
  if (error) {
    HubLogger.miscLogger('udp-Log', `Error posting udp data: ${error.toString()}`, LL.WARN);
  }
};

exports.pushToCLS = function(message, params, appAutomate = false) {
  var deleteAndReturn = function(obj, key) {
    var res = obj[key];
    delete obj[key];
    return res;
  };

  const sessionID = deleteAndReturn(params, 'session_id');
  if (isUndefined(sessionID)) {
    HubLogger.miscLogger("CLS Push", `Invalid session id for message: ${message || ""}`, LL.INFO);
    return;
  }

  exports.checkSessionAppAutomate(sessionID, (isAppAutomate) => {
    const [product, sessionKey] = (appAutomate || isAppAutomate) ? ['app-automate', 'app_automate_session_id'] : ['Automate', 'automate_session_id'];

    const options = {
      [sessionKey]: sessionID,
      product,
      message,
      app_timestamp: new Date(),
      app: 'hub',
      user_id: deleteAndReturn(params, 'user_id'),
      group_id: deleteAndReturn(params, 'group_id'),
      json_data: params,
    };

    HubLogger.miscLogger("CLS Push", "Message: " + message, LL.DEBUG);
    if (isProd) {
      try {
        const byteData = Buffer.from(JSON.stringify(options));
        exports.getUDPSocket().send(byteData, 0, byteData.length,
          constants.cls_port, constants.cls_host, logUDPFailure);
      } catch (err) {
        HubLogger.miscLogger("CLS Push", "Exception " + err.toString() + " while sending message: " + message, LL.DEBUG);
      }
    } else {
      HubLogger.miscLogger("CLS Push", "Should have posted to CLS message: " + message + " data: " + options, LL.DEBUG);
    }
  });
};

exports.sanitizeChromeOptionsArgsArray = function(chromeOptionsArgs) {
  if(!chromeOptionsArgs) {
    return [];
  }
  // Ensure returnValue is always an array
  var returnValue = [];
  if(typeof chromeOptionsArgs === 'string') {
    returnValue = [ chromeOptionsArgs ];
  } else if(typeof chromeOptionsArgs === 'object' && Array.isArray(chromeOptionsArgs)) {
    returnValue = chromeOptionsArgs;
  } else {
    returnValue = [ JSON.stringify(chromeOptionsArgs) ];
  }
  return returnValue;
};

exports.getString = function(variable){
  return (variable || "").toString();
};

exports.randomID = function(length) {
  return crypto.randomBytes(length).toString('hex');
};

exports.getRandom = function(min, max) {
  return Math.random() * ( max - min ) + min;
};

var udpSocket;
exports.getUDPSocket = function() {
  if(isUndefined(udpSocket)){
    udpSocket = udp.createSocket('udp4');
    udpSocket.on('error', function(error){
      var stackTrace = error.stack ? error.stack.toString() : error.toString();
      HubLogger.tempExceptionLogger(`UDP Error - ${error.toString()}`, error, stackTrace);
      udpSocket = null;
    });
  }
  return udpSocket;
};

exports.setTimeout = function(req, timeout, cb) {
  req.once('socket', function(socket){
    socket.removeAllListeners('timeout');
    socket.setTimeout(timeout);
    socket.once('timeout', cb);
  });
};

exports.PingZombie = async function (dataJsonObject) {
  const environment = constants.isProductionEnv ? 'production' : 'staging';

  HubLogger.miscLogger('PingZombie', `Zombie service:${JSON.stringify(dataJsonObject)}`, LL.DEBUG);

  if(constants.BLACKLISTED_ZOMBIE_KINDS.indexOf(dataJsonObject.kind) < 0) {
    try {
      const payloadLength = await DWH.send('udp', 'pager', dataJsonObject, environment);
      HubLogger.miscLogger('PingZombie', `Payload length:${payloadLength}`, LL.DEBUG);
    } catch (err) {
      HubLogger.miscLogger('PingZombie', `Exception ${err.toString()} while sending data: ${JSON.stringify(dataJsonObject)}`, LL.WARN);
    }
  }

  if (dataJsonObject.kind === 'app_automation_session_stats') {
    for (let i = 0; i < constants.APP_AUTOMATE_TEST_SESSIONS_EDS_COLS.length; i++) {
      if (typeof dataJsonObject[constants.APP_AUTOMATE_TEST_SESSIONS_EDS_COLS[i]] !== 'undefined') {
        exports.sendToEDS(dataJsonObject);
        break;
      }
    }
  } else {
    exports.sendToEDS(dataJsonObject);
  }
};

/**
 * Creates hash_id required for Automate Error Data
 */
exports.createHashForAutomateErrorData = (id, timestamp) => {
  return crypto.createHash('sha512').update(`${id}${timestamp}${crypto.randomBytes(10).toString('hex')}`).digest("hex");
};

exports.addToGetOptionsforAdditionalJsonCapsOnPWIOS = function(getOptions) {
  let addingJsonCaps;

  try {
    addingJsonCaps = JSON.parse(getOptions.ADDITIONAL_JSON_CAP_PW_ON_IOS);
  } catch (error) {
    addingJsonCaps = {};
  }

  if(isNotUndefined(getOptions.orientation) && getOptions.orientation == 'landscape') {
    addingJsonCaps["orientation"] = getOptions.orientation.toUpperCase();
  }

  if(isNotUndefined(getOptions.autoAcceptAlerts) && getOptions.autoAcceptAlerts) {
    addingJsonCaps["autoAcceptAlerts"] = true;
    addingJsonCaps["safariAllowPopups"] = true;
  }

  if(Object.keys(addingJsonCaps).length > 0) {
    getOptions.ADDITIONAL_JSON_CAP_PW_ON_IOS = JSON.stringify(addingJsonCaps);
  }
};

exports.sendToEDS = (data) => {
  /* Filter if service=seleniumHub and instrumentationMechanismFlag=0 */
  const instrumentationMechanismFlag = constants.instrumentationMechanismFlag;
  const service = exports.isHubOrInstrumentationService(process.env.CONSUMER_TOPIC);
  if(service === 'SeleniumHub' && instrumentationMechanismFlag === 0 ) {
    Object.keys(data).forEach(key => {
      if(constants.kafkaZombieKeys.includes(key.toString())) {
        delete data[key];
      }
    });
    if(Object.keys(data).length === 2 && data.kind && (data.sessionid || data.session_id || data.hashed_id)) {
      return;
    }
  }

  const dataToSend = {};
  data.hashed_id = data.sessionid || data.hashed_id || data.session_id;

  /* eslint-disable-next-line default-case */
  switch (data.kind) {
    case 'cdp_session_stats':
      dataToSend.event_type = Events.CDP_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case 'playwright_session_stats':
      dataToSend.event_type = Events.PLAYWRIGHT_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case 'automation_session_stats':
      dataToSend.event_type = Events.AUTOMATE_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case 'app_automation_session_stats':
      dataToSend.event_type = Events.APP_AUTOMATE_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case Events.AUTOMATE_SESSION_TIME_COMPONENTS:
      dataToSend.event_type = Events.AUTOMATE_SESSION_TIME_COMPONENTS;
      delete data.kind;
      break;
    case Events.AUTOMATE_TEST_SESSIONS:
      dataToSend.event_type = Events.AUTOMATE_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case Events.AUTOMATE_ERROR_DATA:
      dataToSend.event_type = Events.AUTOMATE_ERROR_DATA;
      delete data.kind;
      break;
    case Events.AUTOMATE_QUEUEING_DATA:
      dataToSend.event_type = Events.AUTOMATE_QUEUEING_DATA;
      delete data.kind;
      break;
    case Events.APP_AUTOMATE_ERROR_DATA:
      dataToSend.event_type = Events.APP_AUTOMATE_ERROR_DATA;
      delete data.kind;
      break;
    case Events.APP_AUTOMATE_QUEUEING_DATA:
      dataToSend.event_type = Events.APP_AUTOMATE_QUEUEING_DATA;
      delete data.kind;
      break;
    case Events.APP_AUTOMATE_TEST_SESSIONS:
      dataToSend.event_type = Events.APP_AUTOMATE_TEST_SESSIONS;
      delete data.kind;
      delete data.sessionid;
      break;
    case Events.AUTOMATE_PERFORMANCE_DATA:
      dataToSend.event_type = Events.AUTOMATE_PERFORMANCE_DATA;
      delete data.kind;
      delete data.sessionid;
      break;
    case Events.APP_AUTOMATE_PERFORMANCE_DATA:
      dataToSend.event_type = Events.APP_AUTOMATE_PERFORMANCE_DATA;
      delete data.kind;
      delete data.sessionid;
      break;
    case 'tcg_frontend_app_logs':
      dataToSend.event_type = 'frontend_app_logs';
      delete data.kind;
      break;
  }

  dataToSend.data = data;
  if (dataToSend.event_type) {
    try {
      HubLogger.miscLogger('sendToEDS', `Sending data to EDS ${JSON.stringify(dataToSend)}`, LL.DEBUG);
      dwhClient.send(dataToSend, (err) => {
        if (err) {
          HubLogger.miscLogger('sendToEDS', `Exception ${err.toString()} while sending to EDS, data: ${dataToSend}`, LL.ERROR);
        }
      }).catch((err) => {
        HubLogger.miscLogger('sendToEDS', `Promise Exception ${err.toString()} while sending to EDS, data: ${dataToSend}`, LL.ERROR);
      });
    } catch (err) {
      HubLogger.miscLogger('sendToEDS', `Exception ${err.toString()} while sending to EDS, data: ${dataToSend}`, LL.INFO);
    }
  }
};

exports.pushBlockExecutionTime = function (hashedId, type, executionTime) {
  exports.sendToEDS({
    'kind': Events.AUTOMATE_SESSION_TIME_COMPONENTS,
    'timestamp': Math.round(new Date().getTime() / 1000),
    'hashed_id': hashedId,
    'source': 'SeleniumHub',
    'type': type,
    'data': JSON.stringify(executionTime)
  });
};

exports.addToConsoleTimes = function (request, label) {
  try {
    if (request && request.id && !request.is_app_automate_session){
      let count = 0;
      if (label.match(/-end$/)) {
        let startLabel = label.slice(0,-4); // removing -end
        count = consoleTimes[`${request.id}-${startLabel}`] ? consoleTimes[`${request.id}-${startLabel}`]["count"] : 0;
      } else {
        count = consoleTimes[`${request.id}-${label}`] ? consoleTimes[`${request.id}-${label}`]["count"] + 1 : 0;
        if(`${request.id}-${label}` in consoleTimes) {
          consoleTimes[`${request.id}-${label}`]["count"] = count;
        }
      }
      label = count ? `${count}_${label}` : label;
      consoleTimes[`${request.id}-${label}`] = {"time": Date.now(), "count": count};
      constants.execution_time_registry[request.id] = constants.execution_time_registry[request.id] || [];
      constants.execution_time_registry[request.id].push(label);
    }
  } catch (err) {
    HubLogger.miscLogger("processExecutionTime", `Exception ${err.toString()} while adding to execution_time_registry label: ${label}`, LL.INFO);
  }
};

// Returns time in msec
exports.getFromConsoleTimes = function (request, label) {
  var time;
  if (request && request.id && isNotUndefined(consoleTimes[`${request.id}-${label}`])) {
    time = parseInt(consoleTimes[`${request.id}-${label}`]["time"]);
  }
  return time;
};

// Returns seconds elapsed since reference time
// reference_time must be in msec
exports.getTimeElapsedSince = (referenceTime) => {
  return new Date().getTime() - referenceTime;
};

exports.isW3C = (keyObject) => {
  return keyObject.dialect ? keyObject.dialect === 'W3C' : false;
};

exports.extractSubUrlFromUrl = (sessionId, url) => {
  var seleniumCommand = url.toString();
  if(seleniumCommand.indexOf('/wd/hub/session') == -1){
    throw new Error(`Invalid Selenium Command - ${url}`);
  }
  if(seleniumCommand.indexOf(`/wd/hub/session/${sessionId}`) != -1){
    seleniumCommand = seleniumCommand.replace(`/wd/hub/session/${sessionId}/`,'');
    seleniumCommand = seleniumCommand.replace(`/wd/hub/session/${sessionId}`,'');
  }
  if(seleniumCommand.indexOf('/wd/hub/session') != -1 ){
    seleniumCommand = seleniumCommand.replace(`/wd/hub/session/`,'');
    seleniumCommand = seleniumCommand.replace(`/wd/hub/session`,'');
  }
  seleniumCommand = seleniumCommand || "";
  return seleniumCommand;
};

exports.extractSeleniumSubCommandFromUrl = (sessionId, url, isW3c) => {
  var seleniumCommandUrl = exports.extractSubUrlFromUrl(sessionId, url);
  var seleniumCommands = seleniumCommandUrl.split("/");
  let seleniumMainCommand = seleniumCommands[0] || "";
  var seleniumSubCommand = "";
  if(isW3c == true) {
    switch(seleniumMainCommand) {
      case 'execute':
      case 'alert':
      case 'window':
        if(seleniumCommands[1]){
          seleniumSubCommand = `${seleniumCommands[1]}`;
        }
        break;
      case 'element':
        if(seleniumCommands[1] && seleniumCommands[1] == 'active'){
          seleniumSubCommand = seleniumCommands[1];
          break;
        }
        if(seleniumCommands[2]){
          seleniumSubCommand = `${seleniumCommands[2]}`;
        }
        break;
      case 'appium':
        seleniumSubCommand = seleniumCommands[2] ||  seleniumCommands[1] || "";
        break;
    }
  } else {
    switch(seleniumMainCommand) {
      case 'ime':
      case 'frame':
      case 'touch':
      case 'local_storage':
      case 'session_storage':
      case 'timeouts':
      case 'log':
        if(seleniumCommands[1]){
          seleniumSubCommand = `${seleniumCommands[1]}`;
        }
        break;
      case 'window':
      case 'element':
        if(seleniumCommands[1] && seleniumCommands[1] == 'active'){
          seleniumSubCommand = seleniumCommands[1];
          break;
        }
        if(seleniumCommands[2]){
          seleniumSubCommand = `${seleniumCommands[2]}`;
        }
        break;
      case 'appium':
        seleniumSubCommand = seleniumCommands[2] ||  seleniumCommands[1] || "";
        break;
    }
  }
  return seleniumSubCommand;
};

exports.extractSeleniumMainCommandFromUrl = (sessionId, url) => {
  let seleniumCommand = exports.extractSubUrlFromUrl(sessionId, url);
  return seleniumCommand.split("/")[0];
};

exports.extractSeleniumCommandFromUrl = (sessionId, url, isW3C) => {
  let seleniumSubCommand = exports.extractSeleniumSubCommandFromUrl(sessionId, url, isW3C);
  var seleniumCommand = exports.extractSeleniumMainCommandFromUrl(sessionId, url);
  if(isNotUndefined(seleniumSubCommand) && seleniumSubCommand != ""){
    seleniumCommand = `${seleniumCommand}_${seleniumSubCommand}`;
  }
  return seleniumCommand;
};

exports.startSeleniumCommandClock = (sessionId, url, requestMethod, seleniumCommandIndex, isAppAutomate, isW3C, group_id, terminalType, seleniumCommandStartTime = Date.now(), nginxOutTime = seleniumCommandStartTime, jarTime = 0, userToNginxTime = 0) => {
  try{
    if(seleniumCommandIndex >= constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION){
      return;
    }
    const seleniumCommand = exports.extractSeleniumCommandFromUrl(sessionId, url, isW3C);
    constants.execution_time_registry[sessionId] = {
        isAppAutomate,
        seleniumCommandIndex,
        requestMethod,
        seleniumCommand,
        seleniumCommandStartTime,
        terminalType,
        nginxOutTime,
        userToNginxTime,
        jarTime
    };
  }
  catch(error){
    const stackTrace = error.stack ? error.stack.toString() : error.toString();
    HubLogger.tempExceptionLogger('startSeleniumCommandClock', error.message, stackTrace, constants.hubName, url);
  }
  return;
};

exports.getMetaDataforCommand = (requestMethod, command, logData) => {
  let meta = "";
  try {
    if(requestMethod === 'POST' && (command === "element" || command === "elements") && logData){
      meta = JSON.parse(logData).using ||  meta;
    }
    else if(requestMethod === 'POST' && command === 'context' && logData){
      meta = JSON.parse(logData).name || meta;
    } else if(requestMethod == 'POST' && command === 'execute_sync' && logData) {
      meta = JSON.parse(logData).script || meta;
    }
  } catch(e) {
    HubLogger.miscLogger('getMetaDataforCommand', `Failed to getMetaDataforCommand for ${logData}`, LL.ERROR);
  }

  return meta;
};

exports.addToHubProcessingRegistry = (requestMethod,sessionId,jarTime,hubProcessingTime,nginxToHubTime,userToNginxTime) => {
  hubProcessingTime = Math.max(hubProcessingTime,0);
  if(requestMethod != 'DELETE'){
    constants.hubProcessingRegistry['hubRequestCount'] += 1;
    constants.hubProcessingRegistry['hubProcessingTime'] += (hubProcessingTime || 0);
    constants.global_registry[sessionId].hubProcessingTime += (hubProcessingTime || 0);
    constants.hubProcessingRegistry['jarTime'] += (jarTime || 0);
    constants.global_registry[sessionId].jarTime += (jarTime || 0);
  }
  constants.hubProcessingRegistry['nginxToHubTime'] += nginxToHubTime;
  constants.global_registry[sessionId].nginxToHubTime += nginxToHubTime;
  constants.hubProcessingRegistry['userToNginxTime'] += userToNginxTime;
  constants.global_registry[sessionId].userToNginxTime += userToNginxTime;
};

exports.resetHubProcessingRegistry = () => {
  const currentHubProcessingRegistryData = constants.hubProcessingRegistry;
  constants.hubProcessingRegistry = {
    'hubRequestCount': 0,
    'hubProcessingTime': 0,
    'nginxToHubTime': 0,
    'jarTime': 0
  };
  return currentHubProcessingRegistryData;
};

exports.stopSeleniumClockAI = (sessionId, url, requestMethod, isW3C, statusCode, aiRetryCount, aiStopTime = Date.now()) => {
  if (isNotUndefined(constants.execution_time_registry[sessionId])){
    try{
      if (isNotUndefined(constants.execution_time_registry[sessionId]['seleniumCommandIndex'])){
        const seleniumCommandIndex = constants.execution_time_registry[sessionId]['seleniumCommandIndex'];
        if(seleniumCommandIndex >= constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION) {
          HubLogger.miscLogger('stopSeleniumClockAI', `Commands for session ${sessionId} exceeded ${constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION}, ignoring kafka push`);
          return;
        }
      }
      const seleniumCommand = exports.extractSeleniumCommandFromUrl(sessionId, url, isW3C);

      if(isNotUndefined(constants.execution_time_registry[sessionId]['aiStartTime'])){
        if(isNotUndefined(constants.global_registry[sessionId])){
          const aiStartTime = constants.execution_time_registry[sessionId]['aiStartTime'];
          const aiCommandDuration = (aiStopTime - aiStartTime);
          const keyObject = constants.global_registry[sessionId];
          const aiSuccess = statusCode === 1;
          constants.global_registry[sessionId]['automate_ai_duration'] += aiCommandDuration;
          constants.global_registry[sessionId]['automate_ai_success'] += aiSuccess;
          constants.global_registry[sessionId]['automate_ai_retry_count'] += aiRetryCount;
          let finalString = `${constants.hubName}:${requestMethod}:${seleniumCommand}_ai:${aiCommandDuration}:${statusCode}:${aiRetryCount}`;

          //send to kafka here
          HubLogger.uploadLogPartToKafka(keyObject, null, `${finalString}\r\n`, kafkaConfig.performance_logs_topic);
        }
      }
    }
    catch(error){
      var stackTrace = error.stack ? error.stack.toString() : error.toString();
      HubLogger.tempExceptionLogger('stopSeleniumClockAI', error.message, stackTrace, constants.hubName, url);
    }
  }
  return;
};

exports.stopSeleniumClock = (sessionId, url, requestMethod, isW3C, statusCode, logData, seleniumStopTime = Date.now()) => {
  if (isNotUndefined(constants.execution_time_registry[sessionId])){
    try{
      if (isNotUndefined(constants.execution_time_registry[sessionId]['seleniumCommandIndex'])){
        const seleniumCommandIndex = constants.execution_time_registry[sessionId]['seleniumCommandIndex'];
        if(seleniumCommandIndex >= constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION) {
          HubLogger.miscLogger('stopSeleniumClock', `Commands for session ${sessionId} exceeded ${constants.MAX_SELENIUM_COMMANDS_INSTRUMENTATION}, ignoring kafka push`);
          return;
        }
      }
      const seleniumCommand = exports.extractSeleniumCommandFromUrl(sessionId, url, isW3C);
      const seleniumStartTime = constants.execution_time_registry[sessionId]['seleniumCommandStartTime'];
      const seleniumCommandDuration = (seleniumStopTime - seleniumStartTime);
      const nginxOutTime = constants.execution_time_registry[sessionId]['nginxOutTime'];
      const isAppAutomate = constants.execution_time_registry[sessionId]['isAppAutomate'];
      const jarTime = constants.execution_time_registry[sessionId]['jarTime'] || seleniumCommandDuration;
      const userToNginxTime = constants.execution_time_registry[sessionId]['userToNginxTime'] || 0;
      if(constants.global_registry[sessionId]){
        constants.global_registry[sessionId].insideHubTime += seleniumCommandDuration;
        if(isAppAutomate || constants.execution_time_registry[sessionId]['terminalType'] === 'desktop'){
          exports.addToHubProcessingRegistry(requestMethod, sessionId, jarTime, seleniumCommandDuration - jarTime, seleniumStartTime - nginxOutTime, userToNginxTime);
        }
      }

      if(isNotUndefined(constants.execution_time_registry[sessionId]['requestMethod'])
      && isNotUndefined(constants.execution_time_registry[sessionId]['seleniumCommand'])
      && isNotUndefined(constants.execution_time_registry[sessionId]['seleniumCommandStartTime'])
      && constants.execution_time_registry[sessionId]['seleniumCommand'] == seleniumCommand
      && constants.execution_time_registry[sessionId]['requestMethod'] == requestMethod
      ){
        if(isNotUndefined(constants.global_registry[sessionId])){
          const keyObject = constants.global_registry[sessionId];
          let finalString = `${constants.hubName}:${requestMethod}:${seleniumCommand}:${seleniumCommandDuration}:${statusCode}`;
          if(isAppAutomate) {
            const meta = exports.getMetaDataforCommand(requestMethod, seleniumCommand, logData);
            finalString += `:${meta}`;
          }
          if (requestMethod === 'POST' && (seleniumCommand === "element" || seleniumCommand === "elements")) {
            if (!(statusCode == 0 || (statusCode >= 200 && statusCode <= 299)) &&
            !constants.global_registry[sessionId].elementNotFound) {
              constants.global_registry[sessionId].elementNotFound = constants.ELEMENT_NOT_FOUND_DETECTED;
            }
          }
          //send to kafka here
          HubLogger.uploadLogPartToKafka(keyObject, null, `${finalString}\r\n`, kafkaConfig.performance_logs_topic);
        }
      }
    }
    catch(error){
      var stackTrace = error.stack ? error.stack.toString() : error.toString();
      HubLogger.tempExceptionLogger('stopSeleniumClock', error.message, stackTrace, constants.hubName, url);
    }
  }
  return;
};

exports.instrumentVideoDuration = (keyObject) => {
  if (isNotUndefined(keyObject) && isNotUndefined(keyObject.platform_video_start_time) && !isNaN(keyObject.platform_video_start_time) && !keyObject.appTesting) {
    const video_duration = Math.round(Date.now()/1000 - parseFloat(keyObject.platform_video_start_time));
    const featureUsage = {"videoLogs" : {"debug_actual_video_duration" : video_duration}}; // instrumentation added for debugging purpose for video duration stability
    instrumentation.pushFeatureUsage(keyObject.rails_session_id, featureUsage, () => {});
  }
};

exports.mapDurationMap = (durationMap) => {
  const newdurationMap = {};
  for (let label in durationMap) {
    let data = label.match(constants.DURATION_MAP_REGEX);
    if (data) {
      if (newdurationMap[data[0]]) {
        newdurationMap[data[0]]["attempts"] += 1;
        newdurationMap[data[0]]["total"] += durationMap[label];
        newdurationMap[data[0]][label] = durationMap[label];
      } else {
        newdurationMap[data[0]] = {"attempts": 1, "total": durationMap[label], ["0_" + label]: durationMap[label]};
      }
    } else {
      newdurationMap[label] = durationMap[label];
    }
  }
  return newdurationMap;
};

exports.isStartRequest = (request) => {
  return request.method === 'POST' && request.url && constants.START_REQUEST_URL_REGEX.test(request.url);
};

exports.isStopRequest = (request) => {
  return request.method === 'DELETE' && request.url && constants.STOP_REQUEST_URL_REGEX.test(request.url);
};

exports.respondWithError = (request, response, errorMessage = '', skipWrite = false, skipResponse = false) => {

  if(!skipResponse) {
    if (!skipWrite) {
      response.write(errorMessage);
    }
    return response.end();
  }
};

exports.markRequestEnd = (request) => {
  this.addToConsoleTimes(request, 'received-request-end');
  this.processExecutionTime(request);
};

exports.processExecutionTime = function (request) {
  if (request && request.id && !request.is_app_automate_session) {
    try {
      var requestId = request.id;
      if (constants.execution_time_registry[requestId]) {
        var executionTimes = constants.execution_time_registry[requestId];
        delete constants.execution_time_registry[requestId];
        var durationMap = { "request_id": requestId};
        var brokenKeys = [];
        executionTimes.forEach((value) => {
          var key = `${requestId}-${value}`;
          if (key.match(/-end$/)) {
            if (isNotUndefined(consoleTimes[key.slice(0,-4)])) {
              return;
            }
            delete consoleTimes[key];
            return;
          }
          var initTime = 0;
          if (consoleTimes[key]) {
            initTime = consoleTimes[key]["time"];
          }
          if (!initTime) {
            return;
          }
          if(consoleTimes[`${key}-end`]){
            var finalTime = consoleTimes[`${key}-end`]["time"];
            if (!finalTime) {
              brokenKeys.push(value);
              delete consoleTimes[key];
              return;
            }
            var duration = finalTime - initTime;
            durationMap[value] = duration;
          }
          delete consoleTimes[key];
          delete consoleTimes[`${key}-end`];
        });
        if (brokenKeys.length > 0) {
          HubLogger.miscLogger("processExecutionTime", `Execution end time missing for ${brokenKeys}. requestId: ` + requestId + " executionTimes: " + executionTimes, LL.INFO);
        }
        if (durationMap["session-creation"] && request.sessionId) {
          exports.pushBlockExecutionTime(request.sessionId, "start", exports.mapDurationMap(durationMap));
        } else if (durationMap["jar-delete"] && request.sessionId) {
          exports.pushBlockExecutionTime(request.sessionId, "stop", durationMap);
        }
      } else {
        HubLogger.miscLogger("processExecutionTime", "execution_time_registry is missing for request: " + request.id, LL.INFO);
      }
    } catch (err) {
      HubLogger.miscLogger("processExecutionTime", `Exception ${err.toString()} while pushing ExecutionTime.`, LL.INFO);
    }
  }
  discardConsoleTimes(request);
};

function discardConsoleTimes(request) {
  if (constants.execution_time_registry[request.id]) {
    let executionTimes = constants.execution_time_registry[request.id];
    delete constants.execution_time_registry[request.id];
    executionTimes.forEach((value) => {
      let key = `${request.id}-${value}`;
      delete consoleTimes[key];
      delete consoleTimes[`${key}-end`];
    });
  }
}

exports.getAutoitText = function(keyObject, textArray, pubSub) {
  var autoitText = '',
    newKeyArray = null;

  keyObject.ieSpecialKeyPress.forEach(function(autoitKey) {
    autoitText += '{' + autoitKey + 'DOWN}#-#';
  });
  textArray.forEach(function(sendKeyText) {
    if(keysMap[sendKeyText]) {
      var englishText = keysMap[sendKeyText].english;
      var useAutoitText = keysMap[sendKeyText].autoit;

      if([ "SHIFT", "CONTROL", "ALT" ].indexOf(englishText) > -1) {
        var autoitKeyIndex = keyObject.ieSpecialKeyPress.indexOf(useAutoitText);
        if(autoitKeyIndex > -1) {
          newKeyArray = keyObject.ieSpecialKeyPress;
          newKeyArray.splice(keysMap[sendKeyText].autoit, 1);
          pubSub.publish(constants.updateKeyObject, {
            session: keyObject.rails_session_id,
            changed: {
              ieSpecialKeyPress: newKeyArray
            }
          });
          autoitText += '{' + useAutoitText + 'UP}#-#';
        } else {
          newKeyArray = keyObject.ieSpecialKeyPress;
          newKeyArray.push(keysMap[sendKeyText].autoit);
          pubSub.publish(constants.updateKeyObject, {
            session: keyObject.rails_session_id,
            changed: {
              ieSpecialKeyPress: newKeyArray
            }
          });
          autoitText += '{' + useAutoitText + 'DOWN}#-#';
        }
      } else {
        autoitText += '{' + useAutoitText + '}#-#';
      }
    } else {
      autoitText += sendKeyText.replace(/(.{1})/g, "$1#-#").replace(/(\{|\}|!|#|\+|\^)#/g, "{$1}#");
    }
  });
  keyObject.ieSpecialKeyPress.forEach(function(autoitKey) {
    autoitText += '{' + autoitKey + 'UP}#-#';
  });
  return autoitText;
};

exports.sanitizeRequestCapsForCLS = (requestBodyString) => {
  var requestCaps = {};
  try {
    requestCaps = JSON.parse(requestBodyString);
  } catch(err) {
    return requestBodyString;
  }
  exports.SanitizeCaps(requestCaps);
  var requestString = JSON.stringify(requestCaps);
  return HubLogger.filterDetails(requestString);
};

exports.SanitizeCaps = function(caps) {
  for (const index in constants.rails_omitted_caps){
    if(caps["desiredCapabilities"][constants.rails_omitted_caps[index]]){
      delete caps["desiredCapabilities"][constants.rails_omitted_caps[index]];
    }
  }
  for (const index in constants.rails_redacted_caps){
    if(caps["desiredCapabilities"][constants.rails_redacted_caps[index]]){
      delete caps["desiredCapabilities"][constants.rails_redacted_caps[index]];
    }
  }
  if(caps['desiredCapabilities'] && caps['desiredCapabilities']['chromeOptions'] && caps['desiredCapabilities']['chromeOptions']['extensions']) {
    delete caps['desiredCapabilities']['chromeOptions']['extensions'];
  }
  return caps;
};


exports.isLocalhostDomainOrIP = function(hostname){
  return hostname && (hostname.match(/localhost/) || hostname.match(/127.0.0/));
};

exports.isPrivateDomainOrIP = function (hostname) {
  return hostname && constants.PRIVATE_DOMAIN_OR_IP_REGEX.some(regex => regex.test(hostname));
};

exports.getRequestObjAndUrlHostname = (requestData, keyObject) => {
  let requestDataUrlHostname = "", requestDataObj = {};
  try {
    requestDataObj = requestData ? JSON.parse(requestData) : {};
  } catch (e) {
    HubLogger.miscLogger('JSONParseError', `Session Id: ${keyObject.rails_session_id} Error in JSON parse, stacktrace: ${e}`, LL.WARN);
    return {requestDataUrlHostname, requestDataObj};
  }
  try {
    requestDataUrlHostname = requestDataObj.url ? urlModule.parse(requestDataObj.url).hostname : '';
  } catch (e) {
    HubLogger.miscLogger('badUrlInParse', `Session Id: ${keyObject.rails_session_id} Error in localtranslate url parse, stacktrace: ${e}`, LL.WARN);
    requestDataUrlHostname = requestDataObj.url ? urlModule.parse(encodeURI(requestDataObj.url)).hostname : '';
  }
  return { requestDataUrlHostname, requestDataObj };
};

function getPortForOS(obj) {
  if(obj.realMobile && (obj.os.match(/android/) || obj.os.match(/ios/) || obj.os.match(/tvos/))) {
    return 45671;
  } else {
    return 45692;
  }
}
exports.getPortForOS = getPortForOS;

exports.getPortForDesktopOS = function(keyObject) {
  var port_no = keyObject.os.indexOf("win") > -1? "4567" : "45671";
  return port_no;
};

const getConsoleStackTrace = function (stack) {
  return stack.toString().split("\n").map(line => line.indexOf("releases") > -1 ? line : undefined).filter(line => line).join(";");
};
exports.getConsoleStackTrace = getConsoleStackTrace;

exports.updateOutsideBrowserstackTime = function(keyObject) {
  keyObject.lastRequestTime = Date.now();
  keyObject.outsideBrowserstackTime = keyObject.outsideBrowserstackTime || 0; // For backward compatibilty
  keyObject.outsideBrowserstackTime += keyObject.lastRequestTime - keyObject.lastResponseTime;
  keyObject.userHubLatency = keyObject.userHubLatency ? Math.min(keyObject.userHubLatency, keyObject.lastRequestTime - keyObject.lastResponseTime) : (keyObject.lastRequestTime - keyObject.lastResponseTime);
  if(constants.instrumentationMechanismFlag !== 2){
    exports.sendDataToInstrumentationService(keyObject, null, {
      updateOutsideBrowserstackTime: {
        lastRequestTime: keyObject.lastRequestTime,
        lastResponseTime: keyObject.lastResponseTime
      }
    });
  }
};

exports.updateAppiumDesktopUsage = function (keyObject, userAgent) {
  if (isUndefined(keyObject.appiumDesktopVersionUsed) && isNotUndefined(userAgent) && (userAgent.includes('appium-desktop') || userAgent.includes('appium-inspector'))) {
    keyObject.appiumDesktopVersionUsed = userAgent;
  }
};

exports.sessionRemovedFromRegionHook = function (keyObject, isTimeout = null, key = null, isGetSessionData = false) {
  if(keyObject) {
    // Should be called every time a session is removed from a region ( region Toggle / any kind of stop )
    HubLogger.miscLogger("sessionRemoved", "Clearing timeout for : " + keyObject.rails_session_id, LL.INFO);
    if(isTimeout === false) exports.updateOutsideBrowserstackTime(keyObject);
    exports.timeoutManagerClearTimeout(keyObject.rails_session_id, keyObject);
  }
  if(isTimeout === true) {
    if(key) {
      pubSub.publish(constants.sessionTimeout, key, keyObject, isGetSessionData);
    } else if(keyObject) {
      pubSub.publish(constants.sessionTimeout, keyObject.rails_session_id, keyObject, isGetSessionData);
    }
  } else if(isTimeout === false) {
    if(key) {
      pubSub.publish(constants.sessionDeletionChannel, key, null, isGetSessionData);
    } else if(keyObject) {
      pubSub.publish(constants.sessionDeletionChannel, keyObject.rails_session_id, null, isGetSessionData);
    }
  }

  HubLogger.miscLogger("sessionRemoved", `Removing session from region for sessionId: ${keyObject.rails_session_id} - Console Stack Trace - ${getConsoleStackTrace(new Error().stack)}`, LL.INFO);
  exports.removeFromGlobalRegistry(keyObject);
};

exports.timeoutManagerGetProcessList = (index) => {
  return constants.timeoutManager.eventsListPrefix + "_" + index;
};

exports.timeoutManagerGetRedisWatchKey = (index) => {
  return constants.timeoutManager.redisKeyPrefix + "_" + index;
};

exports.timeoutManagerGetBackupList = (index) => {
  return constants.timeoutManager.eventsListPrefix + "_" + constants.timeoutManager.backupListPrefix + "_" + index;
};

exports.timeoutManagerGetIndex = (sessionId) => {
  HubLogger.miscLogger("timeoutManager", "Calculating timeoutManagerIndex sessionId: " + sessionId, LL.DEBUG);
  return stringHash(sessionId) % constants.timeoutManager.totalCountThisRegion;
};

// Need keys idle_timeout, timeoutManagerIndex in options
exports.timeoutManagerUpdateTimeout = (sessionId, {
  isPuppeteer = false,
  isPlaywright = false,
  isDetox = false,
  timeoutManagerIndex,
  idle_timeout: idleTimeout,
}) => {
  if (isPuppeteer || isPlaywright || isDetox) return;

  var indexForConsumer = typeof timeoutManagerIndex === 'undefined' ? exports.timeoutManagerGetIndex(sessionId) : timeoutManagerIndex;
  const timeoutManagerZAddTag = constants.timeoutManager.zaddTag;
  let elapsedTime = 0;
  const current = new Date();
  HubLogger.miscLogger("timeoutManager", "Updating timeout for sessionId: " + sessionId + " to " + idleTimeout + " index: " + indexForConsumer, LL.DEBUG);
  redisClientSecond.zadd([constants.timeoutManager.zsetKey, Date.now() + idleTimeout, sessionId], (err) => {
    if (err) {
      HubLogger.miscLogger("timeoutManager", "Got Error while zadding data for session: " + sessionId + " error: " + err, LL.ERROR);
    } else {
      constants.pushToHootHootRegistry[timeoutManagerZAddTag] = constants.pushToHootHootRegistry[timeoutManagerZAddTag] || {};
      constants.pushToHootHootRegistry[timeoutManagerZAddTag].v3 = (constants.pushToHootHootRegistry[timeoutManagerZAddTag].v3 || 0) + 1;
    }
    elapsedTime = Date.now() - current;
    exports.instrumentRedisCommand('updateTimeout', elapsedTime);
  });
};

// Need keys timeoutManagerIndex in options
exports.timeoutManagerClearTimeout = (sessionId, {
  isPuppeteer = false,
  isPlaywright = false,
  isDetox = false,
  timeoutManagerIndex,
}) => {
  if (isPuppeteer || isPlaywright || isDetox) return;

  var indexForConsumer = typeof timeoutManagerIndex === 'undefined' ? exports.timeoutManagerGetIndex(sessionId) : timeoutManagerIndex;
  const timeoutManagerZRemTag = constants.timeoutManager.zremTag;
  let elapsedTime = 0;
  const current = new Date();

  HubLogger.miscLogger("timeoutManager", "Clearing timeout for sessionId: " + sessionId + " index: " + indexForConsumer, LL.DEBUG);
  redisClientSecond.zrem([constants.timeoutManager.zsetKey, sessionId], (err) => {
    if (err) {
      HubLogger.miscLogger("timeoutManager", "Got Error while zreming data for session: " + sessionId + " error: " + err, LL.ERROR);
    } else {
      constants.pushToHootHootRegistry[timeoutManagerZRemTag] = constants.pushToHootHootRegistry[timeoutManagerZRemTag] || {};
      constants.pushToHootHootRegistry[timeoutManagerZRemTag].v3 = (constants.pushToHootHootRegistry[timeoutManagerZRemTag].v3 || 0) + 1;
    }
    elapsedTime = Date.now() - current;
    exports.instrumentRedisCommand('clearTimeout', elapsedTime);
  });
};

exports.kafkaLogProducerErrorToZombie = (kind, topic, session_id) => {
  exports.PingZombie({
    kind,
    data: topic,
    region: constants.region,
    machine: constants.osHostName,
    session_id
  });

  // Instrumentation Fix: This is for marking the session as faulty in our health report
  const featureUsage = {};
  featureUsage[constants.logTypeToFeatureUsageKafkaMap[topic]] = {
    messageDropped: true
  };
  instrumentation.pushFeatureUsage(session_id, featureUsage, () => {});
};

exports.retrieveConsoleLogs = function(keyObject,callback) {
  var options = {
    method: 'POST',
    hostname: keyObject.rproxyHost,
    port: keyObject.port,
    path: "/wd/hub/session/" + keyObject.key + "/log",
    body: Buffer.from(JSON.stringify({'type' : 'browser'}))
  };

  /**
   * In w3c protocol /log endpoint is not supported which is used to retrieve console logs. New endpoint for console logs
   * is /wd/hub/session/<id>/se/log. This endpoint only supports chrome browsers. Refer ACE-190.
   */
  if( keyObject.dialect === 'W3C' ){
    if(keyObject.os === 'android') {
      options.path = "/wd/hub/session/" + keyObject.key + "/log";
    } else {
      options.path = "/wd/hub/session/" + keyObject.key + "/se/log";
    }
  }

  if(keyObject.browser == "firefox") {
    HubLogger.miscLogger("retrieveConsoleLogs", "Fetching console logs from FoxDriver Server.", LL.INFO);
    options.port = constants.foxDriverServerPort;
  }

  var headers = {
    "accept": "application/json",
    "content-type": "application/json; charset=utf-8",
    "content-length": options.body.length,
    "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
  };
  requestlib.appendBStackHostHeader(keyObject.name, headers);
  headers = updateHeadersForSelenium4Jars(keyObject, headers);

  options.headers = headers;
  options.recordJarTime = true;

  requestlib.call(options).then((res) => {
    this.addToJarTime(keyObject.rails_session_id, res);
    HubLogger.miscLogger("retrieveConsoleLogs", "Console Logs request for sessionId: " + keyObject.rails_session_id, LL.DEBUG);
    var jsonData = {};
    try {
      jsonData = JSON.parse(res.data);
    } catch(e) {
      exports.kafkaLogProducerErrorToZombie(constants.KAFKA_LOG_PARSING_ERROR_RETRIEVE, constants.kafkaConfig.console_logs_topic, keyObject.rails_session_id);
      HubLogger.miscLogger("retrieveConsoleLog", "Response is not json for sessionId: " + keyObject.rails_session_id + " Error: " + e.message, LL.INFO);
      callback();
      return;
    }
    callback(jsonData);
  }).catch((_err) => {
    exports.kafkaLogProducerErrorToZombie(constants.KAFKA_LOG_FETCHING_ERROR, constants.kafkaConfig.console_logs_topic, keyObject.rails_session_id);
    HubLogger.miscLogger("retrieveConsoleLogs", "Retrieving console logs failed for sessionId: " + keyObject.rails_session_id, LL.INFO);
    callback();
  });
};

exports.retrieveUploadConsoleLogs = function(keyObject, callback) {
  exports.retrieveConsoleLogs(keyObject, function(logData) {
    if (logData === undefined) {
      callback();
      return;
    }

    // OR statement is to check for w3c protocol. The json for consoleLogs in w3c protocol
    // does not have 'status' in it so using a different check
    if(logData.status === 0 || Array.isArray(logData.value)) {
      exports.appendConsoleLogs(keyObject, JSON.stringify(logData.value));
    }
    callback();
  });
};

exports.appendConsoleLogs = function (keyObject, logString, logObject = undefined) {
  if (!keyObject.appTesting) {
    try {
      if (isUndefined(logObject)) {
        logObject = JSON.parse(logString);
      }

      // For a case where logString is null, JSON.parse gives null
      if (isUndefined(logObject) || !Array.isArray(logObject)) {
        throw new Error('logObject is undefined/null/invalid');
      }
    } catch (e) {
      exports.kafkaLogProducerErrorToZombie(constants.KAFKA_LOG_PARSING_ERROR, constants.kafkaConfig.console_logs_topic, keyObject.rails_session_id);
      // Print logString only when it is convertible to string
      HubLogger.exceptionLogger(`Error parsing console logs for sessionId: ${keyObject.rails_session_id} ${isNotUndefined(logString) ? 'logString: ' + logString.toString().substring(0, 100) : ''}`, '', '', e);
      return;
    }

    // If code reaches here that means json parsing yielded expected results(json object)
    const appendString = logObject.map((logHash) => {
      let logString = '';
      if (isNotUndefined(logHash.timestamp) || isNotUndefined(logHash.level) || isNotUndefined(logHash.message)) {
        logString = `${logHash.timestamp}:${logHash.level}:${logHash.message}\r\n`;
      }
      return logString;
    });
    HubLogger.uploadLogPartToKafka(keyObject, null, appendString.join(''), kafkaConfig.console_logs_topic);
  }
};

// This function is used for uploading console logs from Playwright's connectOverCDP and Puppeteer sessions.
exports.uploadCDPConsoleLog = function (keyObject, data, bsConsoleLogLevel) {
  let eventText;
  let eventType;
  let eventTimestamp;
  const { method: eventMethod } = data;
  let methodInConsoleMethodList = CONSOLE_METHOD_LIST.has(eventMethod);
  if(methodInConsoleMethodList) [eventText, eventType, eventTimestamp] = exports.getMethodAndText(data, eventMethod);
  if (methodInConsoleMethodList && isNotUndefined(eventText) &&
  isNotUndefined(eventType) && isNotUndefined(eventTimestamp) &&
  exports.canUploadConsoleLog(bsConsoleLogLevel, eventType)) {
    const consoleLogsObj = [{
      "message": eventText,
      "level": eventType,
      "timestamp": eventTimestamp
    }];
    exports.appendConsoleLogs(keyObject, JSON.stringify(consoleLogsObj));
  }
};

exports.getMethodAndText = function (data, eventMethod) {
  if(eventMethod === CDP_CONSOLE_API_METHOD) {
    const {
      params: {
        type: eventType,
        args: [{
          value: eventText
        }] = [{}],
        timestamp: eventTimestamp
      } = {}
    } = data;
    return [eventText, eventType, eventTimestamp];
  } else if (eventMethod === CDP_LOG_METHOD) {
    const {
      params: {
        entry: {
          level: eventType,
          text: eventText,
          timestamp: eventTimestamp
        } = {}
      } = {}
    } = data;
    return [eventText, eventType, eventTimestamp];
  }
};

exports.canUploadConsoleLog = function (bsConsoleLogLevel, type) {
  return bsConsoleLogLevel >= (constants.CONSOLE_LOG_LEVELS.get(type) || constants.VERBOSE_LOG_LEVEL);
};

exports.takeScreenshotAndUpload = function(obj, file_counter, _opts) {
  const keyObject = constants.global_registry[obj.rails_session_id];
  if( keyObject && keyObject.os && constants.DEBUGSCREENSHOT_DEPRECATED_OS.includes(keyObject.os.toLowerCase()) ) {
    return;
  }
  obj.debug_screenshot_counter = (obj.debug_screenshot_counter || 0) + 1;
  const file = "screenshot-" + file_counter;
  var snapshotHubParams = {
    bucket: obj.s3bucket,
    folder: obj.rails_session_id,
    file,
    zombie_host: constants.zombie_server,
    zombie_port: constants.zombie_port
  };

  snapshotHubParams['key'] = keyObject && keyObject.aws_new_user_enabled ? constants.DEBUGSCREENSHOT_PUT_AWS_KEY : constants.DEBUGSCREENSHOT_AWS_KEY;
  snapshotHubParams['secret'] = keyObject && keyObject.aws_new_user_enabled ? constants.DEBUGSCREENSHOT_PUT_AWS_SECRET : constants.DEBUGSCREENSHOT_AWS_SECRET;
  if(keyObject && keyObject.instrumentBlackScreenshot) snapshotHubParams['instrumentBlackScreenshot'] = keyObject.instrumentBlackScreenshot;
  if(obj.realMobile) {
    snapshotHubParams["device"] = obj.device;
    snapshotHubParams["orientation"] = obj.deviceOrientation;

    if (/android/.test(obj.os)) {
      snapshotHubParams["useADBforScreenshot"] = true;
    }
  }

  var snapshotUrl = "/snapshot_hub?" + requestlib.getEncodedURLParams(snapshotHubParams);
  obj.pendingScreenshots = (obj.pendingScreenshots || 0) + 1;

  HubLogger.miscLogger("S3-Uploads", "Pending requests for screenshots in " + obj.rails_session_id + ": " + obj.pendingScreenshots + "and filename: " + file, LL.DEBUG);

  var options = {
    method: 'GET',
    path: snapshotUrl,
    hostname: obj.rproxyHost,
    port: getPortForOS(obj),
    timeout: constants.SNAPSHOT_JAR_TIMEOUT,
    headers: requestlib.appendBStackHostHeader(obj.name),
  };

  requestlib.call(options).then((res) => {
    HubLogger.miscLogger("S3-Uploads", "Snapshot request for: " + snapshotUrl + " Response: " + res.statusCode + "Filename: " + file, LL.DEBUG);
    obj.pendingScreenshots--;
  }).catch((e) => {
    obj.pendingScreenshots--;
    if (keyObject && !keyObject.debugFailedEventSent) {
      HubLogger.miscLogger("s3-uploads-eds-event-sent", `SessionId:${obj.rails_session_id} filename:${file}`, LL.INFO);
      const featureUsage = {
        debugLogs: {
          logsDropped: true,
          error: `[${constants.hubName}-${process.pid}] ${e.toString() || "undefined"}`
        }
      };
      instrumentation.pushFeatureUsage(obj.rails_session_id, featureUsage, () => {});
      keyObject.debugFailedEventSent = true;
    }
    HubLogger.exceptionLogger("Upload to S3 failed!! Node failed to response: " + obj.name + " SessionId: " + obj.rails_session_id + "Filename: " + file + "Pending: " + obj.pendingScreenshots + " Error: " + (e ? e.toString() : "undefined") , "S3", "S3-url");
    HubLogger.seleniumStats("hub-snapshot-failed", {"terminal_type": obj.terminal_type, "name": obj.name, "sessionId": obj.key}, (e ? e.toString() : "undefined"), "debug snapshot error", obj.key, "screenshot exception");
  });
};


exports.getDeleteResponseDelayForGroup = async (groupId, sessionDuration, device) => {
  HubLogger.miscLogger("DELETE-RESPONSE-DELAY", `Checking delay for group ${groupId} with duration ${sessionDuration} on device ${device}`, LL.INFO);
  try {
    let devicePresent = constants.DEVICES_WITH_DELAYED_DELETE_RESPONSE.find((deviceRegex) => deviceRegex.test(device.split("-")[0]));
    HubLogger.miscLogger("DELETE-RESPONSE-DELAY", `Device Found : ${devicePresent}`, LL.INFO);
    if (await redisClient.sismember([constants.DELAY_ON_DELETE_RESPONSE_FOR_GROUP, groupId])
        && sessionDuration <= constants.SESSION_TIME_THRESHOLD_FOR_DELAY_IN_DELETE_RESPONSE
        && devicePresent) {
          HubLogger.miscLogger("DELETE-RESPONSE-DELAY", `Delaying Delete Response for group ${groupId} by ${constants.SESSION_DELETE_RESPONSE_DELAY} msecs`, LL.INFO);
          return constants.SESSION_DELETE_RESPONSE_DELAY;
    } else {
      return 0;
    }
  } catch (e) {
    return 0;
  }
};

exports.generateQueuedRequestRetryDelay = function (attempt) {
  const exponentialRetryDelay = QUEUE_REQUEST_EXPONENTIAL_DELAY.DEFAULT_RETRY_DELAY;
  const exponentialRetryDelaySize = exponentialRetryDelay.length;
  const defaultRetryDelay = QUEUE_REQUEST_EXPONENTIAL_DELAY.HARD_RETRY_REQUEST_DELAY;
  let nextRailsRequestTimeout = defaultRetryDelay;

  // The retry delay default is [10, 25, 45]
  // If the attempt is greater than the size of the delay the delay is default of 45 seconds
  // If the attempt is lower than the size of the delay the delay would be one of the [10, 15, 20]
  // thus resulting in 1st attempt at 10, 2nd attempt at 25 and 3rd attempt at 45 seconds

  if (attempt > exponentialRetryDelaySize) {
    nextRailsRequestTimeout = defaultRetryDelay;
  } else {
    nextRailsRequestTimeout = exponentialRetryDelay[(attempt - 1)];
  }
  return nextRailsRequestTimeout;
};

exports.queueRequest = function(request, response, options, retries){
  let userId = options.post_params['u'];
  if(!constants.user_queue_registry[userId]) constants.user_queue_registry[userId] = [];
  constants.user_queue_registry[userId].push(options.queue_id);
  if(!constants.user_queue_registry['callbacks']) constants.user_queue_registry['callbacks'] = {};

  let customRailsTimeOut = exports.generateRailsCustomTimeout(options, retries);
  let nextRailsRequestTimeout = customRailsTimeOut || exports.generateQueuedRequestRetryDelay(retries);

  let retryCallback = () => {
    clearTimeout(retryTimeout);
    delete constants.user_queue_registry['callbacks'][options.queue_id];
    if (constants.user_queue_registry[userId]) {
      var userQueueIndex = constants.user_queue_registry[userId].indexOf(options.queue_id);
      if (userQueueIndex > -1) {
        constants.user_queue_registry[userId].splice(userQueueIndex, 1);
      }
    }
    queueHandler.decrQueue(userId, options.post_params.isAppAutomate, options.post_params.isFunctionalTesting);
    browserstack.postBrowserStack("", options.post_params, request, response, options.callback, options.rails_omitted_caps, options.queue_id, options.attempt, options.firecmd_attempt, options.start_attempt);
  };

  HubLogger.miscLogger('User Queue', `Registered user ${userId} with queue id ${options.queue_id} with retry timeout ${nextRailsRequestTimeout} and retry attempt ${retries}`, LL.INFO);

  var retryTimeout = setTimeout(function() {
    exports.redisClient.lrem('user_queue_' + userId, 0, options.queue_id, (_error, _reply) => { });
    delete options.post_params["hardRelease"];
    retryCallback();
    HubLogger.miscLogger('User Queue', `Hard retry user ${userId} with queue id ${options.queue_id}`, LL.INFO);
  }, nextRailsRequestTimeout);

  constants.user_queue_registry['callbacks'][options.queue_id] = () => {
    retryCallback();
  };
  exports.redisClient.rpush('user_queue_' + userId, options.queue_id);
};

exports.isBrowserStackGroup = (group_id) => {
  return group_id === 2;
};

exports.popNextSessionFromQueue = function(bsdata) {
  if(bsdata && bsdata != "") {
    var data = (typeof(bsdata) == 'string') ? JSON.parse(bsdata) : bsdata;
    var user = data.user;
    var terminal_present = data.terminal && data.terminal.toString() == 'true';
    if(user && !terminal_present) {
      redisClient.lpop('user_queue_' + user, function(error, reply) {
        if(reply) {
          HubLogger.miscLogger("User Queue", "Popup - User: " + user + " - Queue id: " + reply, LL.INFO);
          pubSub.publish(constants.userQueuePop, {user: user, queue_id: reply});
        }
      });
    }
  }
};

exports.getSanitizedStringForZombie = function (baseString) {
  // UDP listener handles ASCII not UTF-8
  return baseString ? baseString.toString()
    .substring(0, constants.ZOMBIE_MAX_STRING_SIZE)
    .replace(/[^a-z0-9 \-,.?!/:]/ig, '')
    : '';
};

exports.isHubOrInstrumentationService = function(envTopic) {
  if(!envTopic) {
    return 'SeleniumHub';
  } else if(envTopic === 'instrumentation_logs') {
    return 'Instrumentation Service';
  } else {
    return '';
  }
};

exports.convertPubSubKafkaKeysToCodes = (data) => {
  const newData = {};
  Object.keys(data).forEach((key) => {
    newData[constants.pubSubKafkaKeysProducerCodes[key] || key] = data[key];
    if(getType(data[key]) === 'object' && Object.keys(data[key]).length) {
      newData[constants.pubSubKafkaKeysProducerCodes[key] || key] = {};
      Object.keys(data[key]).forEach(keyNested => {
        newData[constants.pubSubKafkaKeysProducerCodes[key] || key][constants.pubSubKafkaKeysProducerCodes[keyNested] || keyNested] = data[key][keyNested];
      });
    }
    if(constants.instrumentationRedactionKeys.includes(key)) {
      newData[key] = '[REDACTED]';
    }
  });
  return newData;
};

exports.convertPubSubKafkaCodesToKeys = (messageObj) => {
  const typeReq = messageObj.initKeyObject ? 'START' : messageObj.data ? 'CMD:DEL' : null;
  const data = typeReq === 'START' ? messageObj.initKeyObject : typeReq === 'CMD:DEL' ? messageObj.data : null;
  if(typeReq && data) {
    const newData = {};
    Object.keys(data).forEach(key => {
      newData[constants.pubSubKafkaKeysConsumerCodes[key] || key] = data[key];
      if(getType(data[key]) === 'object' && Object.keys(data[key]).length) {
        newData[constants.pubSubKafkaKeysConsumerCodes[key] || key] = {};
        Object.keys(data[key]).forEach(keyNested => {
          newData[constants.pubSubKafkaKeysConsumerCodes[key] || key][constants.pubSubKafkaKeysConsumerCodes[keyNested] || keyNested] = data[key][keyNested];
        });
      }
    });

    if(typeReq === 'START') {
      messageObj.initKeyObject = newData;
    } else if(typeReq === 'CMD:DEL') {
      messageObj.data = newData;
    }
  }
};

exports.sendDataToInstrumentationService = function(keyObject, type, dataToSend, isTimeout = false) {
  dataToSend = exports.convertPubSubKafkaKeysToCodes(dataToSend);
  if(type === 'STOP_SESSION') {
    // Stop session messages can exceed kafka push limit so to enable chunking, send delete data as atomic log and send another blank push with stop session params so that kafka uploader pings hilPipeline to start processing
    // Delete data push as atomic log to enable chunking
    HubLogger.uploadLogPartToKafka(keyObject, null, JSON.stringify({ uniqueId: `${constants.osHostName}-${process.pid}`, data: dataToSend, isTimeout: isTimeout }) + '\r\n', kafkaConfig.instrumentation_logs_topic);
    // blank push after small delay so that stop is received(not written to session log file so no change in hil processing) and session instrumentation processing starts
    setTimeout(()=>{
      HubLogger.uploadLogPartToKafka(keyObject, type, JSON.stringify({ uniqueId: `${constants.osHostName}-${process.pid}`, data: {}, isTimeout: isTimeout }) + '\r\n', kafkaConfig.instrumentation_logs_topic);
    },100);
  } else if(type === 'REQUEST_START') {
    HubLogger.uploadLogPartToKafka(keyObject, type, JSON.stringify({
      uniqueId: `${constants.osHostName}-${process.pid}`,
      initKeyObject: dataToSend
    }) + '\r\n', kafkaConfig.instrumentation_logs_topic);
  } else if(!type) {
    HubLogger.uploadLogPartToKafka(keyObject, type, JSON.stringify({ uniqueId: `${constants.osHostName}-${process.pid}`, data: dataToSend }) + '\r\n', kafkaConfig.instrumentation_logs_topic);
  }
};

exports.hilErrorInstrumentation = function(rails_session_id, isTimeout = null, err = null) {
  if(isTimeout === true || isTimeout === false) {
    exports.PingZombie({
      kind: 'hub-instrumentation-kafka-stop-push-errors',
      data: `topic: ${kafkaConfig.instrumentation_logs_topic}; sessionId: ${rails_session_id}; uniqueId: ${constants.osHostName}-${process.pid}; isTimeout: ${isTimeout}`,
      region: constants.region,
      machine: constants.osHostName,
    });
  } else if(!isTimeout) {
    exports.PingZombie({
      kind: 'hub-instrumentation-pubSub-processing-errors',
      data: `topic: ${kafkaConfig.instrumentation_logs_topic}; sessionId: ${rails_session_id}; uniqueId: ${constants.osHostName}-${process.pid}; err: ${err}`,
      region: constants.region,
      machine: constants.osHostName,
    });
  }
};

exports.removeFromGlobalRegistry = function(host_params) {
  if(!host_params) {
    //Occurs when SO_TIMEOUT has occured and then client sends stop request. Its cool for now, just log it.
    // HubLogger.exceptionLogger("Host Params null for delete: " + util.inspect(host_params) + "\nStack: " + new Error().stack, '', "Delete");
    return;
  }

  //Send data to zombie
  // Depending on value of instrumentationMechanismFlag and running service, pingZombie calls might filter a few fields which will be sent from Instrumentation Service instead
  const sessionKeyObj = constants.global_registry[host_params.rails_session_id], instrumentationMechanismFlag = constants.instrumentationMechanismFlag;

  if (sessionKeyObj) {
    const {
      hubTime = 0,
      request_count: requestCount = 0,
      appTesting = false,
      seleniumRequestsCount = 0,
      toggle,
      outsideBrowserstackTime = 0,
      socketMessages = 0,
      dialect = "OSS",
      userHubLatency = 0,
      hubHostName = null,
      optimalHubUrl = null,
      server_port = null,
    } = sessionKeyObj;
    let { insideHubTime = 0, hubProcessingTime = 0, nginxToHubTime = 0, userToNginxTime = 0, jarTime = 0 } = sessionKeyObj;
    const isRegionToggle = (toggle ? true : false);
    let tertiaryParams = { is_region_toggle: isRegionToggle, outside_bs_time_new: outsideBrowserstackTime, server_port: server_port, dialect };
    const sessionid = host_params.rails_session_id;
    const terminalType = host_params.terminal_type;
    const kind = (appTesting ? 'app_' : '') + 'automation_session_stats';
    tertiaryParams.hub_hostname = hubHostName;
    exports.PingZombie({
      session_id: sessionid,
      kind,
      bs_latency_mean: hubTime / 1000.0,
      inside_bs_network_time: appTesting ? null : insideHubTime,
      number_of_requests_nginx:  requestCount,
      customer_session_duration: (hubTime - insideHubTime) / 1000.0,
      last_opened_url: sessionKeyObj.georestricted_region ? null : exports.getSanitizedStringForZombie(host_params.lastOpenedUrl),
      first_opened_url: sessionKeyObj.georestricted_region ? null : exports.getSanitizedStringForZombie(host_params.firstOpenedUrl),
      selenium_requests_count: appTesting ? null : seleniumRequestsCount,
      tertiary_params: tertiaryParams,
      outside_bs_time: outsideBrowserstackTime,
      feature_usage: { optimalHubUrl: optimalHubUrl }
    });

    let featureUsageObj = {
      sentFrom: 'S',
      latency: {
        minUserToHubLatency: userHubLatency
      }
    };
    if (socketMessages > 0) {
      featureUsageObj.ws = { messageCount: socketMessages };
    }

    if (appTesting || terminalType === 'desktop'){
      featureUsageObj.hubProcessing = {};
      if(hubProcessingTime){
        //hubProcessingTime: How much time was spent in hub for a command
        featureUsageObj.hubProcessing.hubTime = hubProcessingTime;
      }
      if(nginxToHubTime){
        //nginxToHubTime: Command received on hub - nginxOutTime
        featureUsageObj.hubProcessing.nginxToHubTime = nginxToHubTime;
      }
      if(userToNginxTime){
        //userToNginxTime: derived from x-rtt flag given by nginx
        featureUsageObj.hubProcessing.userToNginxTime = userToNginxTime;
      }
      if(jarTime){
        featureUsageObj.hubProcessing.jarTime = jarTime;
      }
    }

    if(instrumentationMechanismFlag !== 0) instrumentation.pushFeatureUsage(sessionid, featureUsageObj, () => {});

    if (appTesting && instrumentationMechanismFlag !== 0) {
      const product = { performance: {} };
      product.performance.total_appium_requests_count = seleniumRequestsCount;
      product.performance.total_inside_bs_time = insideHubTime / 1000.0;
      product.performance.total_outside_bs_time = outsideBrowserstackTime;
      exports.PingZombie({ sessionid, kind, product });
    }

    if (getInstrumentationData(sessionKeyObj)){
      instrumentation.pushFeatureUsage(sessionid, getInstrumentationData(sessionKeyObj), () => {});
    }

    // Note: "POST:push_file" command to be tracked for automate as well. Check for future addition of commands
    const commandUsage = constants.global_registry[sessionid].appiumCommandUsage;
    if(commandUsage) {
      commandUsage.session_id = sessionid;
      exports.pushToCLS('command_usage_tracking_appium', commandUsage, appTesting);
    }

    if (!sessionKeyObj.isSessionStartedUsingAppiumDesktop && isNotUndefined(sessionKeyObj.appiumDesktopVersionUsed)) {
      const appiumDesktopFeatureUsage = { ADAttachSession: sessionKeyObj.appiumDesktopVersionUsed };
      instrumentation.pushFeatureUsage(sessionid, appiumDesktopFeatureUsage, () => {});
    }
  }
  // Remove from the memcache if present
  ha.deleteData(host_params.rails_session_id);
  AICommandHandler.handleRedisKeyRemoval(host_params.rails_session_id);
  sessionManagerHelper.removeFromMemory(host_params);
};

exports.didReachPrivoxyTimeoutForSafari = function(request_time, response_time) {
  return (Date.parse(response_time) - Date.parse(request_time) > 200000); // privoxy timeout is 200 secs
};

exports.automateErrorDataParams = (sessionKeyObj) => {
  return {
    user_id: sessionKeyObj.user_id,
    terminal_sub_region: sessionKeyObj.terminalSubRegion || (sessionKeyObj.browserstackParams && sessionKeyObj.browserstackParams["browserstack.terminal_sub_region"]), // option for start error flow and _getLoggingOpts for firecmd
    georestricted_region: sessionKeyObj.georestricted_region || (sessionKeyObj.browserstackParams && sessionKeyObj.browserstackParams["browserstack.georestricted_region'"]),  // option for start error flow and _getLoggingOpts for firecmd
    terminal_ip: sessionKeyObj.name,
    os: sessionKeyObj.os_with_version || sessionKeyObj.os || (sessionKeyObj.bsCaps && sessionKeyObj.bsCaps['orig_os']),  // option for start error flow and _getLoggingOpts for firecmd
    hub_region: constants.region,
    session_id: sessionKeyObj.rails_session_id,
    browser: sessionKeyObj.browser || (sessionKeyObj.bsCaps && (sessionKeyObj.bsCaps['browserName'] || sessionKeyObj.bsCaps['browser'])), // option for start error flow and _getLoggingOpts for firecmd
    browser_version: sessionKeyObj.browser_version || (sessionKeyObj.bsCaps && (sessionKeyObj.bsCaps['version'] || sessionKeyObj.bsCaps['device'])), // option for start error flow and _getLoggingOpts for firecmd
    device: sessionKeyObj.device || (sessionKeyObj.bsCaps && sessionKeyObj.bsCaps['udid']), // option for start error flow and _getLoggingOpts for firecmd
    rproxyHost: sessionKeyObj.rproxyHost
  };
};

exports.pingDataToStats = function(host_params){
  const instrumentationMechanismFlag = constants.instrumentationMechanismFlag;
  var pre_quit_bucket = "", bs_bucket = "", hash = "", secondary_diagnostic_reason = "";
  if(host_params.exceptionClass && host_params.exceptionClass.match(/unreachablebrowserexception/i)){
    hash = host_params.exceptionRequest || host_params.lastRequest || "";
    if(host_params.browser.match(/safari/i) && (hash == "POST:url" || hash == "POST:click" || hash == "POST:refresh" || hash == "POST:submit"))
      pre_quit_bucket = bs_bucket = "page load";
    else if (host_params.browser.match(/safari/i))
      pre_quit_bucket = bs_bucket = "customer";
    else if(host_params.isWebDriverIOSession)
      pre_quit_bucket = "crash_webdriverio";
    else
      pre_quit_bucket = "crash";
  }
  else if ((host_params.exceptionClass && host_params.exceptionClass.match(/timeoutexception/i)) ||
    (host_params.exceptionMessage && host_params.exceptionMessage.match(/timeout|Cannot navigate to/i))) {
    hash = host_params.exceptionRequest || '';
    if (hash == 'POST:url' || hash == 'POST:click' || hash == 'POST:refresh' || hash == 'POST:submit') {
      pre_quit_bucket = bs_bucket = 'page load';
    }
    else if (hash == 'POST:execute') {
      pre_quit_bucket = bs_bucket = 'browser';
    }
    else {
      pre_quit_bucket = bs_bucket = 'customer';
    }
  }
  else if(host_params.exceptionClass && host_params.exceptionClass.match(/nullpointerexception|outofmemoryerror/i)) {
    pre_quit_bucket = bs_bucket = "selenium";
  }
  else if (host_params.exceptionMessage && host_params.exceptionMessage.match(/Appium error/i)) {
    pre_quit_bucket = bs_bucket = 'appium';
  }
  else if(host_params.exceptionClass){
    pre_quit_bucket = bs_bucket = "customer";
  }
  if (host_params.safariPrivoxyTimeout) {
    pre_quit_bucket = "safariPrivoxyTimeout";
  }
  if(isNotUndefined(host_params.errorMessage)) {
    let errorMessage = undefined;
    try {
      errorMessage = JSON.parse(host_params.errorMessage);
    } catch (error) {
      HubLogger.miscLogger("pingDataToStats", 'Unable to parse errorMessage for sessionid: ' + host_params.rails_session_id, LL.INFO);
    }
    pre_quit_bucket = errorMessage.name;
    secondary_diagnostic_reason = errorMessage.message.toString();
  }
  if(host_params && host_params.user && host_params.hub_to_term_error) {
    let isMobile = false;
    const device = (host_params.device);
    isMobile = !!device;
    var hoothootPlatform = host_params.appTesting ? 'all' : isMobile ? 'mobile' : 'desktop';
    HubLogger.hoothoot_user.uniqueUserEvent(host_params.user, (host_params.appTesting ? 'app-automate' : 'automate'), 'hub_to_term', hoothootPlatform, host_params.hoothootCanaryTags);
    const extraParams = exports.automateErrorDataParams(host_params);
    if (!host_params.appTesting) HubLogger.hoothoot_use.emit('automate_errors_data', 1, { event_type: 'hub_to_term', platform: hoothootPlatform, product: 'automate', ...extraParams }, 15);
  }

  let dataToSend = {
    'sessionid': host_params.rails_session_id,
    'stop_time': ((new Date()) - host_params.stop_init_time),
    'check_url': (host_params.check_url || '').replace(/'/g, ''),
    'pre_quit': (host_params.exceptionClass || host_params.exceptionMessage || '') + '::' + (host_params.exceptionRequest || ''),
    'pre_quit_bucket': pre_quit_bucket,
    'bs_bucket': bs_bucket,
    'total_sleep': host_params.sleepTime,
    'num_sleep': host_params.numSleep,
    'kind': (host_params.appTesting ? 'app_automation_session_stats' : 'automation_session_stats'),
    'last_response': host_params.lastResponseStatus,
  };

  if(isNotUndefined(secondary_diagnostic_reason)) {
    dataToSend['secondary_diagnostic_reason'] = secondary_diagnostic_reason;
  }

  if(!host_params.appTesting  && host_params.nonZeroStatusesCount && Object.keys(host_params.nonZeroStatusesCount).length > 0) {
    const totalCount = Object.entries(host_params.nonZeroStatusesCount).reduce((sum, [key, value]) => key === "total" ? sum : sum + value, 0);

    host_params.nonZeroStatusesCount["total"] = totalCount;
    dataToSend.selenium_exception_counts = host_params.nonZeroStatusesCount;
  }

  /**
   * last_request will be sent for successful, idle_timeout and error sessions.
   * Existence of 'lastRequestDetails' means the session had an error at some point
   * of its lifetime.
   */
  if (host_params.lastRequestDetails) {
    dataToSend.last_request = host_params.lastRequestDetails;
    /**
     * Deleting the lastRequestDetails so that if the flow ever comes in this block again
     * for the same session, we won 't be overriding it with old data.
     * And if the flow comes in this block again, it would come with the updated field for
     * 'lastRequest'. Thus we would move to the 'else' block in that case.
     */
    delete host_params.lastRequestDetails;
  } else {
    dataToSend.last_request = host_params.lastRequest;
  }
  // Only stop_time and last_request to be sent from seleniumHub if instrumentationMechanismFlag = 0
  const service = exports.isHubOrInstrumentationService(process.env.CONSUMER_TOPIC);
  if (instrumentationMechanismFlag === 0) {
    if(service === 'SeleniumHub') {
      Object.keys(dataToSend).forEach(key => {
        if(!(['sessionid','kind','stop_time','last_request','last_response'].includes(key.toString()))) {
          delete dataToSend[key];
        }
      });
    } else if(service === 'Instrumentation Service') {
      delete dataToSend['stop_time'];
      if(dataToSend['last_request']) delete dataToSend['last_request'];
      if(dataToSend['last_response']) delete dataToSend['last_response'];
    }
  }
  if(service === 'SeleniumHub' || (service === 'Instrumentation Service' && instrumentationMechanismFlag === 0)) {
    exports.PingZombie(dataToSend);
  } else if(service === 'Instrumentation Service' && instrumentationMechanismFlag === 1) {
    instrumentation.pushFeatureUsage(host_params.rails_session_id, { flagOneData: { pingDataToStats: { data: dataToSend } } }, () => {});
  }
  exports.sendNonZeroStatusCountToZombie(host_params);
};

exports.getGlobalExceptionGenre = function(errorString) {
  if(errorString.match(/socket hang up/i)) {
    return "socket_error";
  } else if(errorString.match(/EADDRINUSE/i)) {
    return "address_in_use";
  } else if(errorString.match(/ECONNRESET/i)) {
    return "econn_reset";
  } else if(errorString.match(/EPIPE/i)) {
    return "epipe";
  } else if(errorString.match(/ETIMEDOUT/i)) {
    return "etimedout";
  } else if(errorString.match(/Client network socket disconnected before secure TLS connection was established/i)) {
    return "network_tls";
  }
  return "default";
};


exports.hoothootPusher = (hoothootKey, data, tags) => {
  HubLogger.hoothoot.emit(hoothootKey, data, tags);
};

exports.pushStatsToHootHoot = function(hoothootKey, metrics, uniqueId) {
  Object.keys(metrics).forEach(function(key){
    if (typeof metrics[key] == 'object') {
      Object.keys(metrics[key]).forEach(function(nestedKey){
        HubLogger.hoothoot.emit(hoothootKey, metrics[key][nestedKey], { genre: key + "_" + nestedKey , worker: (constants.worker_id || 0), uniqueId: uniqueId });
      });
    } else {
      HubLogger.hoothoot.emit(hoothootKey, metrics[key], { genre: key , worker: (constants.worker_id || 0), uniqueId: uniqueId });
    }
  });
};

exports.handleSessionsWithStopFailedOnMaxRetries = function(sessionId, isAppTesting) {
  var queue = isAppTesting ? constants.APP_AUTOMATE_SESSION_WITH_STOP_FAILED : constants.AUTOMATE_SESSION_WITH_STOP_FAILED;
  exports.redisClient.rpush([queue, sessionId], function(error, _reply){
    if(error){
      HubLogger.miscLogger("Hub", 'REDIS ERROR in SESSION_WITH_STOP_FAILED: ' + error + " for sessionid: " + sessionId, LL.INFO);
    }
    HubLogger.miscLogger("Hub", 'sessionid: ' + sessionId + ' added to redis SESSION_WITH_STOP_FAILED ', LL.INFO);
  });
};

exports.getReadableMinute = () => new Date().toISOString().match(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/)[0];

exports.getReverseOrientation = function (currentOrientation) {
  if (currentOrientation && currentOrientation.toLowerCase().indexOf('portrait') > -1) {
    return 'LANDSCAPE';
  }
  return 'PORTRAIT';
};

exports.getNonZeroRedisKey = function(request_method, request_url, _hashed_id) {
  var requestUrlSplit = request_url ? request_url.split("/") : [ "" ];
  var requestUrl = requestUrlSplit[requestUrlSplit.length - 1];
  var today = new Date();
  var suffix = "" + today.getUTCFullYear() + "_" + ( today.getUTCMonth() + 1 ) + "_" + today.getUTCDate() + "_" + ( request_method || "" ) + "_" + requestUrl;
  return "NON_ZERO_STATUS_" + suffix;
};

exports.ownRedisMetrics = function() {
  redisClient.set(constants.owner_redis_alerts_redis_key, process.pid, ()=>{});
};

exports.checkOwnRedisMetrics = async () => {
  try {
    const data = await redisClient.get(constants.owner_redis_alerts_redis_key);
    const numericPid = data | 0;
    return numericPid === process.pid;
  } catch (err) {
    return false;
  }
};

exports.convertUnicodeToASCIIForSendKeys = function(request) {
  if (request.url.match(/keys$/) || request.url.match(/\/element\/.+\/value$/)){
    request.log_data = request.log_data.split("").map((character)=> keysMap[character] ? keysMap[character].english : character).join("");
  }
};

exports.checkNonZeroStatusErrors = function(keyObject) {
  if(keyObject.nonZeroIncrementCounters && keyObject.nonZeroIncrementCounters.length > 0) {
    exports.shouldLogNonZeroStatusErrors(function() {
      keyObject.nonZeroIncrementCounters.forEach(function(nonZeroErrorKey) {
        ha.incrementCounter(nonZeroErrorKey + '_counter', function(error, reply) {
          var currentErrors = parseInt(reply);
          var trackHashedId = !error && !isNaN(currentErrors) && currentErrors < constants.maxNonZeroDayCount;
          HubLogger.miscLogger("nonZeroKeyIncrement", 'Incremented Key: ' + nonZeroErrorKey + " for sessionid: " + keyObject.rails_session_id + " to " + currentErrors + " TrackHashedId: " + trackHashedId, LL.INFO);
          if(trackHashedId) {
            ha.trackNonZeroStatusError(nonZeroErrorKey, keyObject.rails_session_id, function(_error, _reply) {
              HubLogger.miscLogger("nonZeroKeyIncrement", "Tracked nonZeroStatus Error for sessionid: " + keyObject.rails_session_id + " key: " + nonZeroErrorKey, LL.INFO);
            });
          }
        });
      });
    }, function() {
      // Not Logging NonZeroStatusErrors
    });
  }
};

exports.checkUdpKeystoSend = function(keyObject) {
  if(keyObject && keyObject.udpKeys && Object.keys(keyObject.udpKeys).length > 0) {
    HubLogger.miscLogger("udpKeys", "Sessionid: " + keyObject.rails_session_id + " sending UDP Keys" + JSON.stringify(keyObject.udpKeys), LL.INFO);
    var sendJson = {
      "sessionid": keyObject.rails_session_id,
      "kind": (keyObject.appTesting ? "app_automation_session_stats" : "automation_session_stats")
    };
    Object.keys(keyObject.udpKeys).forEach(function(key) {
      sendJson[key] = keyObject.udpKeys[key];
    });
    exports.PingZombie(sendJson);
  }
};

exports.shouldCheckTitleAfterOpenUrl = function(yes_callback, no_callback) {
  if(constants.forceCheckTitleAfterOpenUrl && constants.forceCheckTitleAfterOpenUrl.toString() === "true") {
    yes_callback();
    return;
  }
  redisClient.get("check_title_after_open_url", function(error, reply) {
    if(!error && reply && reply.toString() && reply.toString().match(/true/)) {
      yes_callback();
      return;
    }
    no_callback();
    return;
  });
};

exports.shouldLogNonZeroStatusErrors = function(yes_callback, no_callback) {
  redisClient.get("log_non_zero_status_errors", function(error, reply) {
    if(!error && reply && reply.toString() && reply.toString().match(/true/)) {
      yes_callback();
      return;
    }
    no_callback();
    return;
  });
};

exports.sendAlerts = alertManager.sendAlerts;

exports.resetPipelineQueue = () => new Promise((resolve) => {
  exports.redisClientSecond.multi()
    .del(constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag)
    .del(constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag)
    .exec(resolve);
});

exports.isBlockedRequest = function(request_url, is_app_automate) {
  var blocked_reqs = is_app_automate ? constants.blocked_requests_app_automate : constants.blocked_requests;
  return blocked_reqs.some((element) => {
    return request_url.match(new RegExp(element));
  });
};

exports.canRetryAppiumStartRequest = function(error_message){
  return constants.APPIUM_START_ERROR_RETRY_CASES.some((retry_case) => {
    return error_message.match(retry_case);
  });
};

exports.checkActiveWindowOnTerminal = function(keyObject) {
  requestlib.call({
    hostname: keyObject.rproxyHost,
    port: ( keyObject.os && keyObject.os.indexOf("mac") > -1 ) ? 45671 : 4567,
    path: "/active_window",
    headers: requestlib.appendBStackHostHeader(keyObject.name),
  }).then((res) => {
    var parsedData = exports.getParsedObjectOrEmpty(res.data);

    HubLogger.miscLogger('POST:url', 'activeWindowCheck session: ' + keyObject.rails_session_id + ' active_window: ' + parsedData["active_window"], LL.INFO);

    if(parsedData["active_window"] || parsedData["active_window_bucket"]) {
      var zombieOpts = {
        "sessionid": keyObject.rails_session_id,
        "active_window": parsedData["active_window"],
        "active_window_bucket": parsedData["active_window_bucket"],
        "kind": keyObject.appTesting ? "app_automation_session_stats" : "automation_session_stats"
      };
      // Pushing this additionally as active window value might be overridden
      if(parsedData["active_window"] && constants.activeWindowBasicAuthValues.some((windowValue) => { return windowValue.match(parsedData["active_window"].toString().toLowerCase()); })) {
        zombieOpts["secondary_diagnostic_reason"] = "SOTIMEOUT-customer-basic-auth-required";
        zombieOpts["secondary_diagnostic_reasons"] = "SOTIMEOUT-customer-basic-auth-required";
        keyObject.hasBasicAuthPopup = true;
        pubSub.publish(constants.updateKeyObject, {
          session: keyObject.rails_session_id,
          changed: {
            hasBasicAuthPopup: true
          }
        });
      }
      exports.PingZombie(zombieOpts);
    }
  }).catch((e) => {
    HubLogger.miscLogger('POST:url', 'activeWindowCheck session: ' + keyObject.rails_session_id + ' error: ' + e.toString(), LL.ERROR);
  });
};


// Inject JavaScript on page to make a GET request on the mini before opening a popup.
exports.generateAllowPopupsScript =  function(mini_public_ip, device_id) {
  return `orig =  window.open
  intercept_window_open = function(message) {
        var i = document.createElement("img");
        i.src = "http://${mini_public_ip}:45671/accept_alert?device=${device_id}";
        orig(message)
        xhttp.send();
      };
  window.open = intercept_window_open;`;
};

// Set firefoxOptions to expose Remote Debugging Port which
// FoxDriver Server will connect to fetch console logs.
// Refer: https://github.com/browserstack/foxdriver-server
//        https://github.com/saucelabs/foxdriver
exports.setOptionsToStartRemoteDebuggerFirefox = function(post_json) {
  if(post_json["moz:firefoxOptions"] == undefined) {
    post_json["moz:firefoxOptions"] = {};
  }

  if(post_json["moz:firefoxOptions"]["args"] == undefined) {
    post_json["moz:firefoxOptions"]["args"] = [];
  }

  // TODO:
  // Handle case where start-debugger-server is already present.
  post_json["moz:firefoxOptions"]["args"].push('-start-debugger-server');
  post_json["moz:firefoxOptions"]["args"].push('9222');

  if(post_json["moz:firefoxOptions"]["prefs"] == undefined) {
    post_json["moz:firefoxOptions"]["prefs"] = {};
  }

  var prefs = {
    'devtools.chrome.enabled': true,
    'devtools.debugger.prompt-connection': false,
    'devtools.debugger.remote-enabled': true
  };

  post_json["moz:firefoxOptions"]["prefs"] = Object.assign(post_json["moz:firefoxOptions"]["prefs"], prefs);
};

/**
 * Deep Merges sourceHash into targetHash.
 * Doesn't override values already present in targetHash
 */
exports.deepMerge = (sourceHash, targetHash) => {
  Object.entries(sourceHash).forEach(([k, v]) => {
    if (exports.isHash(sourceHash[k]) && exports.isHash(targetHash[k])) {
      targetHash[k] = exports.deepMerge(sourceHash[k], targetHash[k]);
    } else if (typeof(targetHash[k]) === 'undefined') {
      targetHash[k] = v;
    }
  });
  return targetHash;
};

exports.isHubCanary = constants.browserstackBuildVersionJSON.machine_type === 'canary';

exports.getHubCanaryTag = () => {
  return exports.isHubCanary ? {hub_canary: true} : undefined;
};

exports.versionCompare = (versionString1, versionString2) => {
  const vString1 = versionString1.split('.');
  const vString2 = versionString2.split('.');
  const minLen = Math.min(vString1.length, vString2.length);
  for (let index = 0; index < minLen; index++ ) {
    if (parseInt(vString1[index]) !== parseInt(vString2[index])) {
      return parseInt(vString1[index]) - parseInt(vString2[index]);
    }
  }
  return vString1.length - vString2.length;
};

const getISOString = () => new Date().toISOString();
exports.getISOString = getISOString;

/**
 *  Method that updates Header for Selenium 4 Jars
 * @param { String } seleniumVersion - Version of Selenium Jar
 * @param { Object } headers - Header object that needs to be updated
 */
const updateHeadersForSelenium4Jars = ({selenium_version: seleniumVersion}, headers) => {

  if (seleniumVersion && exports.versionCompare(seleniumVersion,'4.0.0') >= 0) {
    headers.host = 'localhost';
  }
  return headers;
};

exports.updateHeadersForSelenium4Jars = updateHeadersForSelenium4Jars;


const shouldCheckJarTime = (rails_session_id, res) => {
  return isNotUndefined(rails_session_id) && isNotUndefined(constants.execution_time_registry[rails_session_id]) && isNotUndefined(res) && isNotUndefined(res.currJarTime);
};

exports.shouldCheckJarTime = shouldCheckJarTime;

/**
 *  Method that validates the response and adds the currently calculated jar time to total jar time.
 * @param { String } rails_session_id - rails_session_id.
 * @param { Object } res - Response object.
 */
const addToJarTime = (rails_session_id, res) => {
  if (shouldCheckJarTime(rails_session_id, res)) {
    constants.execution_time_registry[rails_session_id].jarTime += res.currJarTime;
  }
  if (isNotUndefined(res) && isNotUndefined(res.currJarTime)) {
    delete res.currJarTime;
  }
};

exports.addToJarTime = addToJarTime;

// In case of app automate, nonZeroStatusesCount field in keyObject maintains hash of non zero status codes and their count. At the end of the session, this function is called and data is sent to zombie.
exports.sendNonZeroStatusCountToZombie = function (keyObject) {
  const instrumentationMechanismFlag = constants.instrumentationMechanismFlag, service = exports.isHubOrInstrumentationService(process.env.CONSUMER_TOPIC);
  if(keyObject && keyObject.appTesting && keyObject.nonZeroStatusesCount && Object.keys(keyObject.nonZeroStatusesCount).length > 0) {
    var totalCount = 0;
    var zombieDataJson = {};
    Object.entries(keyObject.nonZeroStatusesCount).forEach(([key, value]) => {
      totalCount += value;
      var jwpCode = (jwpMap[key] || key);
      zombieDataJson[jwpCode] = value;
    });

    var zombie_kind = 'app-jsonwp-failed-status-count';
    var zombieJson = {
      'error': totalCount,
      'kind': zombie_kind,
      'session_id': keyObject.rails_session_id,
      'timestamp':(Math.round(new Date().getTime() /1000).toString()),
      'data': zombieDataJson,
      'os': keyObject.os,
      'os_version': keyObject.os_version,
      'device': keyObject.device,
      'browser': keyObject.deviceName,
      'browser_version': keyObject.browser_version,
      'user_id': keyObject.user_id,
      'region': constants.hubName,
      'machine': keyObject.name,
      'url': keyObject.exceptionMessage ? keyObject.exceptionMessage.substr(0, 30) : ""
    };

    if((service === 'SeleniumHub' && instrumentationMechanismFlag !== 0) || (service === 'Instrumentation Service' && instrumentationMechanismFlag === 0)) {
      exports.PingZombie(zombieJson);

      exports.PingZombie({
        "sessionid": keyObject.sessionId || keyObject.rails_session_id,
        "kind": "app_automation_session_stats",
        "secondary_diagnostic_reason": "mid-session-non-zero-status"
      });
    } else if (service === 'Instrumentation Service' && instrumentationMechanismFlag === 1) {
      instrumentation.pushFeatureUsage(keyObject.rails_session_id, { flagOneData: { sendNonZeroStatusCountToZombie: { zombieJson: zombieJson } } }, () => {});
      instrumentation.pushFeatureUsage(keyObject.sessionId || keyObject.rails_session_id, { flagOneData: { sendNonZeroStatusCountToZombie: { '2': { "secondary_diagnostic_reason": "mid-session-non-zero-status" } } } }, () => {});
    }
  }
};

const getAutomationNameErrorMessage = function (automationName) {
  const errorMessage = errorMessages.APP_AUTOMATE_AUTOMATION_ERROR_MESSAGES[automationName];
  if (exports.isDefined(errorMessage)) {
    return errorMessage;
  }
  return '';
};
exports.getAutomationNameErrorMessage = getAutomationNameErrorMessage;

exports.getAutomationNameErrorMessage = getAutomationNameErrorMessage;

exports.getCustomMessageForError = function (options, keyValMessage) {
  if(isString(keyValMessage)) {
    if (isTrueString(options.bsCaps['autoWebview']) && keyValMessage.includes('No such context found')) {
      keyValMessage += errorMessages.APP_AUTOMATE_CONTEXT_START_ERROR;
    } else if (keyValMessage.includes('shell pm clear')) {
      keyValMessage += errorMessages.APP_AUTOMATE_PM_CLEAR;
    } else if (keyValMessage.includes('Set the orientation, but app refused to rotate')) {
      keyValMessage += errorMessages.APP_AUTOMATE_ORIENTATION_FAILED_ERROR;
    } else if (isTrueString(options.bsCaps['autoWebview']) && options.bsCaps.mobile && exports.getOsVersion(options.bsCaps.mobile.version) >= 14.5 && browserstackErrorUtil.detectMissingAppIdKey(keyValMessage)) {
      keyValMessage += errorMessages.APP_AUTOMATE_MISSING_APP_ID_KEY_ERROR.replace("<current_device_name>", options.bsCaps.mobile.version);
    } else if (options.bsCaps["automationName"]) {
      keyValMessage += getAutomationNameErrorMessage(options.bsCaps["automationName"].toLowerCase());
    }
  }
  return keyValMessage;
};

exports.isMobile = function (params) {
  let isMobile = false;
  try {
    let jsonParsedParams = params;
    if (params.desiredCapabilities) {
      if (typeof params.desiredCapabilities === 'string') {
        jsonParsedParams = JSON.parse(unescape(params.desiredCapabilities));
      } else {
        jsonParsedParams = params.desiredCapabilities;
      }
    }
    const device = jsonParsedParams.device || jsonParsedParams.deviceName;
    isMobile = !!device;
  } catch (e) {
    isMobile = false;
  }
  return isMobile;
};

exports.getHoothootKeyForQueueing = function (product) {
  return (product === 'app-automate') ? constants.monitoring.reqToTimeKeyAppAutomate : constants.monitoring.reqToTimeKeyAutomate;
};

exports.addToQueueingStatsHoothoot = function (appTesting, requestId) {
  const product = appTesting ? 'app-automate' : 'automate';
  const reqToTimeKey = exports.getHoothootKeyForQueueing(product);
  redisClientSecond.zadd([reqToTimeKey, Date.now(), requestId]);
};

exports.removeFromQueueingStatsHoothoot = function (requestId) {
  redisClientSecond.zrem([constants.monitoring.reqToTimeKeyAppAutomate, requestId]);
  redisClientSecond.zrem([constants.monitoring.reqToTimeKeyAutomate, requestId]);
};

exports.getFireCMDErrorMessage = function (opts, keyObject) {
  let errorMessage = "Could not start Browser / Emulator";

  if (opts["terminal_type"] == "realMobile") {
    errorMessage = "Could not start Mobile Browser.";

    const bsCapsObject = exports.nestedKeyValue(keyObject, ['bsCaps']);
    if (exports.isDefined(bsCapsObject) && keyObject.bsCaps["automationName"]) {
      errorMessage += getAutomationNameErrorMessage(keyObject.bsCaps["automationName"].toLowerCase());
    }
  } else if (opts["terminal_type"] == "app_testing") {
    errorMessage = constants.firecmd_custom_exceptions[opts["error_type"]] || constants.firecmd_custom_exceptions["unknown_exception"];
  }

  if(constants.exceptions_needing_templating.includes(opts["error_type"]) && opts["error_meta_data"]) {
    errorMessage = exports.stringTemplating(errorMessage, opts["error_meta_data"]);
  }
  return errorMessage;
};

const updateStopReleaseUrlParams = (keyObject, sessionHash, queryParams, postParams, requestType) => {
  queryParams.k = keyObject.rails_session_id;
  queryParams.local_key = keyObject.key;

  if (keyObject.sessionStartedAt) {
    postParams.sessionStartedAt = keyObject.sessionStartedAt;
  }
  if (sessionHash && sessionHash.secondary_state != constants.secondary_states.SUCCESS) {
    postParams.s = sessionHash.secondary_state;
  }
  if (keyObject.device) {
    queryParams.device = keyObject.device;
    postParams.device = keyObject.device;
  }
  if (keyObject.appTesting) {
    queryParams.app = true;
    postParams.isAppAutomate = true;
  }
  if (keyObject.captureCrash) {
    queryParams.capture_crash = true;
  }
  if (keyObject.exceptionEncountered) {
    postParams.sessionWithException = true;
  }
  if (isTrueString(keyObject.isPuppeteer)) {
    postParams.framework = 'puppeteer';
  } else if (isTrueString(keyObject.isPlaywright)) {
    postParams.framework = 'playwright';
  } else if(isTrueString(keyObject.isDetox)) {
    postParams.framework = 'detox';
  }

  if (Qig.shouldSendDataToRails(keyObject)) {
    postParams.qig = keyObject.qig;
  }

  if (requestType === 'stop') {
    postParams.userRequestStartTimeDiff = exports.getTimeElapsedSince(keyObject.userRequestStartTime);
    postParams["client_ip"] = keyObject["client_ip"];
    postParams["client_connection_sockets_count"] = keyObject.clientConnectionSocketsCount;
    postParams["request_count"] = keyObject.request_count;
    if (keyObject.lastOpenedUrl) {
      postParams["lastOpenedUrl"] = keyObject.lastOpenedUrl;
    }
    if (keyObject.elementNotFound) {
      postParams["elementNotFound"] = keyObject.elementNotFound;
    }
    if (keyObject.pageLoadError) {
      postParams["pageLoadError"] = keyObject.pageLoadError;
    }
    if (keyObject.lighthouseAutomate) {
      postParams["lhReportList"] = keyObject.lighthouseAutomate.finalList;
      postParams["lhReportCount"] = keyObject.lighthouseAutomate.report_counter;
    } else {
      postParams["lhReportCount"] = 0;
    }
  }
  if (keyObject.ai_healing_details && keyObject.ai_healing_details.total_healing_enabled_request > 0) {
    postParams.ai_healing_details = keyObject.ai_healing_details;
  }
  postParams.selfHealingSuccess = keyObject.selfHealingSuccess;
  postParams.softHealingSuccess = keyObject.softHealingSuccess;
  postParams.midSessionHealingDisabled = keyObject.midSessionHealingDisabled;
  postParams.nudgeLocalNotSetError = keyObject.nudgeLocalNotSetError;
};

const buildRailsStopUrlParams = (keyObject, sessionHash, stopReason = 'CLIENT_STOPPED_SESSION') => {
  let stopPostParams = {};
  let stopQueryParams = {
    stop: true,
    i: keyObject.name,
    r: stopReason,
  };

  updateStopReleaseUrlParams(keyObject, sessionHash, stopQueryParams, stopPostParams, 'stop');
  return { stopUrl: `&${requestlib.getEncodedURLParams(stopQueryParams)}`, stopPostParams };
};

exports.buildRailsStopUrlParams = buildRailsStopUrlParams;

const buildRailsReleaseUrlParams = (keyObject, sessionHash, reasonException) => {
  const lastRequests = sessionHash.lastRequestDetails ? sessionHash.lastRequestDetails : "";
  const lastRawLog = sessionHash.lastRawLog ? sessionHash.lastRawLog : "";
  let releasePostParams = {
    r: reasonException,
    last_request: lastRequests.split("::")[0],
    last_raw_log: lastRawLog,
  };
  let releaseQueryParams = {
    release: true,
    ip: keyObject.name,
    capture_crash: true
  };
  updateStopReleaseUrlParams(keyObject, sessionHash, releaseQueryParams, releasePostParams, 'release');
  return {
    releaseUrl: `&${requestlib.getEncodedURLParams(releaseQueryParams)}`, releasePostParams,
  };
};

exports.buildRailsReleaseUrlParams = buildRailsReleaseUrlParams;

// Appium 1.15.0 & 1.16.0 while running on w3c as false does not return status key in response
// So bypassing status key for these specific version
exports.checkResponseStatus = function (jsonData, keyObject, statusCode) {
  return jsonData.status === 0 || (statusCode === 200 && keyObject.dialect === 'W3C') || (keyObject.os == "android" && ['1.15.0', '1.16.0'].indexOf(keyObject.appium_version) >= 0);
};

// Proxy polling enabled for below mentioned conditions
exports.checkDevicesForProxyPolling = function (keyObject) {
  return (keyObject.browser === 'MicrosoftEdge') || (keyObject.browser === 'internet explorer' && keyObject.browser_version === "11.0");
};

exports.zipAlignCheck = function (getOptions, options) {
  if ((options.bsCaps["orig_os"].toLowerCase() === "android") && options.bsCaps["zip_align"]) {
    getOptions.zip_align = options.bsCaps["zip_align"];
  }
  return;
};

exports.setAccessibilityAutomationConfigJSON = function (getOptions, options) {
  if ((options.browserstackParams["browserstack.accessibility"] || false).toString() === 'true') {
    getOptions.accessibility = true;
    if (options.browserstackParams["browserstack.accessibilityOptions"]) {
      getOptions.accessibilityOptions = JSON.stringify({
        scannerVersion: options.browserstackParams["browserstack.accessibilityOptions"].scannerVersion,
        wcagVersion: options.browserstackParams["browserstack.accessibilityOptions"].wcagVersion,
        scannerProcessingTimeout: options.browserstackParams["browserstack.accessibilityOptions"].scannerProcessingTimeout,
        includeIssueType: {
          needsReview: this.nestedKeyValue(options.browserstackParams, ["browserstack.accessibilityOptions", 'includeIssueType', 'needsReview']),
          bestPractice: this.nestedKeyValue(options.browserstackParams, ["browserstack.accessibilityOptions", 'includeIssueType', 'bestPractice'])
        },
        auth: options.browserstackParams["browserstack.accessibilityOptions"].authToken,
      });
    } else {
      getOptions.accessibilityOptions = JSON.stringify({
        scannerVersion: options.browserstackParams["browserstack.accessibilityOptions.scannerVersion"],
        wcagVersion: options.browserstackParams["browserstack.accessibilityOptions.wcagVersion"],
        scannerProcessingTimeout: options.browserstackParams["browserstack.accessibilityOptions.scannerProcessingTimeout"],
        includeIssueType: {
          needsReview: options.browserstackParams["browserstack.accessibilityOptions.includeIssueType.needsReview"],
          bestPractice: options.browserstackParams["browserstack.accessibilityOptions.includeIssueType.bestPractice"],
        },
        auth: options.browserstackParams["browserstack.accessibilityOptions.authToken"],
      });
    }
  }
};

exports.addAccessibilityAutomationPresignedURL = function (browserstackParams, railsOmittedCaps) {
  if ((browserstackParams["browserstack.accessibility"] || false).toString() !== 'true') {
    return;
  }
  if(!railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY]) {
    return;
  }
  if (browserstackParams["browserstack.accessibilityOptions"]) {
    browserstackParams["browserstack.accessibilityOptions"].scannerVersion = railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY];
  } else {
    browserstackParams["browserstack.accessibilityOptions.scannerVersion"] = railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY];
  }
  delete railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY];
};

exports.a11yIsURL = function (value) {
  // very basic URL check;
  if(value === undefined || value === null) return false;
  return ((value.length > 3) && (value.substr(0,4) === "http"));
};

exports.a11yGetReplacementValue = function (value) {
  // if value is a URL strip out the signature and then return URL
  if (exports.a11yIsURL(value)) {
    return value.split('?')[0];
  }
  return value;
};

exports.a11yIsPresignedURL = function (value){
  if (exports.a11yIsURL(value)) {
    return (value.split('?').length > 1);
  }
  return false;
};

exports.removeAccessibilityAutomationPresignedURL = function (caps, railsOmittedCaps) {
  let presignedUrl = undefined;
  let presignedUrlCopy = undefined;
  // unfortunately .? does not works
  if (caps["desiredCapabilities"] &&
      caps["desiredCapabilities"]["bstack:options"] &&
      caps["desiredCapabilities"]["bstack:options"]["accessibilityOptions"] &&
      caps["desiredCapabilities"]["bstack:options"]["accessibilityOptions"]["scannerVersion"]
    ) {
    presignedUrlCopy = caps["desiredCapabilities"]["bstack:options"]["accessibilityOptions"]["scannerVersion"];
    if(exports.a11yIsPresignedURL(presignedUrlCopy)){
      presignedUrl = presignedUrlCopy;
      caps["desiredCapabilities"]["bstack:options"]["accessibilityOptions"]["scannerVersion"] = exports.a11yGetReplacementValue(presignedUrlCopy);
    }
  }

  if (
    caps["desiredCapabilities"] &&
    caps["desiredCapabilities"]["accessibilityOptions"] &&
    caps["desiredCapabilities"]["accessibilityOptions"]["scannerVersion"]
  ) {
    presignedUrlCopy = caps["desiredCapabilities"]["accessibilityOptions"]["scannerVersion"];
    if(exports.a11yIsPresignedURL(presignedUrlCopy)){
      presignedUrl = presignedUrlCopy;
      caps["desiredCapabilities"]["accessibilityOptions"]["scannerVersion"] = exports.a11yGetReplacementValue(presignedUrlCopy);
    }
  }
  if (
    caps["desiredCapabilities"] &&
    caps["desiredCapabilities"]["browserstack.accessibilityOptions.scannerVersion"]
  ) {
    presignedUrlCopy = caps["desiredCapabilities"]["browserstack.accessibilityOptions.scannerVersion"];
    if(exports.a11yIsPresignedURL(presignedUrlCopy)){
      presignedUrl = presignedUrlCopy;
      caps["desiredCapabilities"]["browserstack.accessibilityOptions.scannerVersion"] = exports.a11yGetReplacementValue(presignedUrlCopy);
    }
  }

  if (caps["capabilities"] &&
    caps["capabilities"]["alwaysMatch"] &&
    caps["capabilities"]["alwaysMatch"]["bstack:options"] &&
    caps["capabilities"]["alwaysMatch"]["bstack:options"]["accessibilityOptions"] &&
    caps["capabilities"]["alwaysMatch"]["bstack:options"]["accessibilityOptions"]["scannerVersion"]
  ) {
    presignedUrlCopy = caps["capabilities"]["alwaysMatch"]["bstack:options"]["accessibilityOptions"]["scannerVersion"];
    if(exports.a11yIsPresignedURL(presignedUrlCopy)){
      presignedUrl = presignedUrlCopy;
      caps["capabilities"]["alwaysMatch"]["bstack:options"]["accessibilityOptions"]["scannerVersion"] = exports.a11yGetReplacementValue(presignedUrlCopy);
    }
  }

  if (caps["capabilities"] &&
    caps["capabilities"]["alwaysMatch"] &&
    caps["capabilities"]["alwaysMatch"]["accessibilityOptions"] &&
    caps["capabilities"]["alwaysMatch"]["accessibilityOptions"]["scannerVersion"]
  ) {
    presignedUrlCopy = caps["capabilities"]["alwaysMatch"]["accessibilityOptions"]["scannerVersion"];
    if(exports.a11yIsPresignedURL(presignedUrlCopy)){
      presignedUrl = presignedUrlCopy;
      caps["capabilities"]["alwaysMatch"]["accessibilityOptions"]["scannerVersion"]  = exports.a11yGetReplacementValue(presignedUrlCopy);
    }
  }

  if (caps["capabilities"] &&
    caps["capabilities"]["firstMatch"] &&
    caps["capabilities"]["firstMatch"][0] &&
    caps["capabilities"]["firstMatch"][0]["bstack:options"] &&
    caps["capabilities"]["firstMatch"][0]["bstack:options"]["accessibilityOptions"] &&
    caps["capabilities"]["firstMatch"][0]["bstack:options"]["accessibilityOptions"]["scannerVersion"]
  ) {
    presignedUrlCopy = caps["capabilities"]['firstMatch'][0]["bstack:options"]["accessibilityOptions"]["scannerVersion"];
    if(exports.a11yIsPresignedURL(presignedUrlCopy)){
      presignedUrl = presignedUrlCopy;
      caps["capabilities"]['firstMatch'][0]["bstack:options"]["accessibilityOptions"]["scannerVersion"]  = exports.a11yGetReplacementValue(presignedUrlCopy);
    }
  }

  if (caps["capabilities"] &&
    caps["capabilities"]["firstMatch"] &&
    caps["capabilities"]["firstMatch"][0] &&
    caps["capabilities"]["firstMatch"][0]["accessibilityOptions"] &&
    caps["capabilities"]["firstMatch"][0]["accessibilityOptions"]["scannerVersion"]
  ) {
    presignedUrlCopy = caps["capabilities"]['firstMatch'][0]["accessibilityOptions"]["scannerVersion"];
    if(exports.a11yIsPresignedURL(presignedUrlCopy)){
      presignedUrl = presignedUrlCopy;
      caps["capabilities"]['firstMatch'][0]["accessibilityOptions"]["scannerVersion"] = exports.a11yGetReplacementValue(presignedUrlCopy);
    }
  }
  // Make sure scanner version is a URL..if not a URL don't omit.
  if(exports.a11yIsPresignedURL(presignedUrl)){
    railsOmittedCaps[constants.A11Y_PRESIGNED_URL_KEY] = presignedUrl;
  }
};

/*
--host-resolver-rules arg for chromeOptions will not be honored in case local in on.
Local will be given more priority.
*/
exports.hostResolverHonored = (
    platform,
    chromeOptions,
    browserstackTunnel,
    isDedicatedDevice=false,
    isMobileDataFlagPresent=false
  ) => {
  if (isAndroid(platform) &&
    (isDedicatedDevice && isMobileDataFlagPresent) || // If mobile data is enabled on dedicated device then disable proxy
    (!isTrueString(browserstackTunnel) && // check if local is enabled
    isNotUndefined(chromeOptions) &&
    isNotUndefined(chromeOptions["args"]) &&
    Array.isArray(chromeOptions["args"]) &&
    chromeOptions["args"].some(arg => arg && arg.toString().split("=")[0] === "--host-resolver-rules"))
  ) {
    return true;
  }
  return false;
};

exports.sendSeleniumRequest = async (method, path, hostParams, headers, body = {}) => {
  const options = {
    method: method,
    path: path,
    headers: headers,
    hostname: hostParams.rproxyHost,
    port: hostParams.port,
    timeout: constants.NODE_DIED_IN,
  };

  if (body) {
    options.body = JSON.stringify(body);
    options.headers["content-length"] = JSON.stringify(options.body.length);
  }

  options.headers = exports.updateHeadersForSelenium4Jars(hostParams, options.headers);
  requestlib.appendBStackHostHeader(hostParams.name, options.headers);

  const response = await requestlib.call(options);

  return response.data;
};

exports.flakySamsungTabChrome = async (hostParams, request, callback) => {
  let headers = request.headers || {};
  constants.IDEMPOTENCY_KEY_HEADER.forEach((key) => {
    delete headers[key]; // appium key decides if we need to cache the response, causes issues as it caches errors too.
  });
  try {
    const sessionId = hostParams.key;

    await exports.sendSeleniumRequest(
      "POST",
      `/wd/hub/session/${sessionId}/execute/sync`,
      hostParams,
      headers,
      {
        script: 'window.open("about:blank", "_blank");',
        args: []
      }
    );

    await exports.sendSeleniumRequest(
      "DELETE",
      `/wd/hub/session/${sessionId}/window`,
      hostParams,
      headers
    );

    const handles = await exports.sendSeleniumRequest(
      "GET",
      `/wd/hub/session/${sessionId}/window/handles`,
      hostParams,
      headers
    );

    const newTabHandle = JSON.parse(handles).value[0];

    await exports.sendSeleniumRequest(
      "POST",
      `/wd/hub/session/${sessionId}/window`,
      hostParams,
      headers,
      { handle: newTabHandle }
    );
  } catch (err) {
    HubLogger.newCGLogger("FLAKY-SAMSUNG-TAB-CHROME", `flakySamsungTabChrome failed for ${hostParams.rails_session_id} Error: ${err ? err.toString() : "undefined"}`, LL.ERROR, hostParams.rails_session_id);
  }
  callback();
};

exports.setContextSamsungMobile = async (hostParams, request, retries, callback) => {
  if (retries > 10) {
    exports.PingZombie({
      "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
      "session_id": hostParams.rails_session_id,
      "data": "samsung browser max retries reached for set context",
      "kind": "aut_samsung_contextset_failed_final",
      "user_id": hostParams.user_id,
      "os": hostParams.os
    });
    instrumentation.pushFeatureUsage(hostParams.rails_session_id, {"samsungSetContextError" : `set context max retries reached`}, () => {});
    HubLogger.newCGLogger("SET-CONTEXT-SAMSUNG", `setContextSamsungMobile max retries reached for ${hostParams.rails_session_id} retries: ${retries}`, LL.ERROR, hostParams.rails_session_id,);
    callback();
    return;
  }
  let headers = request.headers || {};
  constants.IDEMPOTENCY_KEY_HEADER.forEach((key) => {
    delete headers[key]; // appium key decides if we need to cache the response, causes issues as it caches errors too.
  });
  let body = JSON.stringify({"name" : "WEBVIEW_Terrace"});
  let options = {
    method: "POST",
    path: `/wd/hub/session/${hostParams.key}/context`,
    headers: headers,
    hostname: hostParams.rproxyHost,
    host: hostParams.rproxyHost,
    port: hostParams.port,
    body: body,
    timeout: constants.NODE_DIED_IN,
  };
  options.headers = exports.updateHeadersForSelenium4Jars(hostParams, options.headers);
  requestlib.appendBStackHostHeader(hostParams.name, options.headers);
  options.headers["content-length"] = JSON.stringify(options.body.length);
  try {
    const res = await requestlib.call(options);
    if (res.statusCode.toString() !== "200") {
      instrumentation.pushFeatureUsage(hostParams.rails_session_id, {"samsungSetContextError" : `set context ${res.statusCode.toString()} status code`}, () => {});
      await exports.sleep(2000);
      await exports.setContextSamsungMobile(hostParams, request, retries+1, callback);
      return;
    }
    HubLogger.miscLogger("setContextSamsungMobile", `setcontext to ${body} for samsung mobile sessionid: ${hostParams.rails_session_id} statusCode: ${res.statusCode.toString()} data: ${res.data}`, LL.INFO);
    HubLogger.newCGLogger("SET-CONTEXT-SAMSUNG", `setContextSamsungMobile setcontext to ${body} for samsung mobile sessionid: ${hostParams.rails_session_id} statusCode: ${res.statusCode.toString()} data: ${res.data}`, LL.INFO, hostParams.rails_session_id);
    callback();
  } catch (err) {
    HubLogger.newCGLogger("SET-CONTEXT-SAMSUNG", `setContextSamsungMobile failed for ${hostParams.rails_session_id} Error: ${err ? err.toString() : "undefined"}`, LL.ERROR, hostParams.rails_session_id);
    exports.PingZombie({
      "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
      "session_id": hostParams.rails_session_id,
      "data": err ? err.toString().substring(0, 1024) : "undefined",
      "kind": "aut_samsung_contextset_failed",
      "user_id": hostParams.user_id,
      "os": hostParams.os
    });
    await exports.sleep(2000);
    await exports.setContextSamsungMobile(hostParams, request, retries+1, callback);
    return;
  }
};

exports.installHealingExtensionFirefox = async (hostParams, request, retries, setPageLoadOnFetchDataCallback, activateBrowser) => {
  if (retries > 5) {
    exports.PingZombie({
      "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
      "session_id": hostParams.rails_session_id,
      "data": "firefox extension installation max retries reached for AI healing",
      "kind": "install_self_healing_ext",
      "user_id": hostParams.user_id,
      "os": hostParams.os
    });
    instrumentation.pushFeatureUsage(hostParams.rails_session_id, {"aiHealingFirefoxExtension" : `max retries reached for installing FF ext`}, () => {});
    HubLogger.newCGLogger("installHealingExtensionFirefox", `installHealingExtensionFirefox max retries reached for ${hostParams.rails_session_id} retries: ${retries}`, LL.ERROR, hostParams.rails_session_id,);
    setPageLoadOnFetchDataCallback(activateBrowser);
    return;
  }
  let headers = request.headers || {};
  let body = JSON.stringify({"addon" : constants.AI_EXTENSIONS["firefox"], "temporary" : true});
  let options = {
    method: "POST",
    path: `/wd/hub/session/${hostParams.key}/moz/addon/install`,
    headers: headers,
    hostname: hostParams.rproxyHost,
    host: hostParams.rproxyHost,
    port: hostParams.port,
    body: body,
    timeout: constants.NODE_DIED_IN,
  };
  options.headers = exports.updateHeadersForSelenium4Jars(hostParams, options.headers);
  requestlib.appendBStackHostHeader(hostParams.name, options.headers);
  options.headers["content-length"] = JSON.stringify(options.body.length);
  try {
    const res = await requestlib.call(options);
    if (res.statusCode.toString() !== "200") {
      instrumentation.pushFeatureUsage(hostParams.rails_session_id, {"aiHealingFirefoxExtension" : `aiHealingFirefoxExtension ${res.statusCode.toString()} status code`}, () => {});
      await exports.sleep(2000);
      await exports.installHealingExtensionFirefox(hostParams, request, retries+1, setPageLoadOnFetchDataCallback, activateBrowser);
      return;
    }
    HubLogger.newCGLogger("installHealingExtensionFirefox", `installHealingExtensionFirefox installed FF AI extension for sessionid: ${hostParams.rails_session_id} statusCode: ${res.statusCode.toString()} data: ${res.data}`, LL.INFO, hostParams.rails_session_id);
    setPageLoadOnFetchDataCallback(activateBrowser);
  } catch (err) {
    HubLogger.newCGLogger("installHealingExtensionFirefox", `installHealingExtensionFirefox failed for ${hostParams.rails_session_id} Error: ${err ? err.toString() : "undefined"}`, LL.ERROR, hostParams.rails_session_id);
    exports.PingZombie({
      "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
      "session_id": hostParams.rails_session_id,
      "data": err ? err.toString().substring(0, 1024) : "undefined",
      "kind": "install_self_healing_ext",
      "user_id": hostParams.user_id,
      "os": hostParams.os
    });
    await exports.sleep(2000);
    await exports.installHealingExtensionFirefox(hostParams, request, retries+1, setPageLoadOnFetchDataCallback, activateBrowser);
    return;
  }
};

const setContextIOSChrome = async (hostParams, request, retries, callback) => {
  try {
    if (retries > 10) {
      exports.PingZombie({
        "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
        "session_id": hostParams.rails_session_id,
        "data": "ios chrome browser max retries reached for set context",
        "kind": "aut_ios_chrome_contextset_failed_final",
        "user_id": hostParams.user_id,
        "os": hostParams.os
      });
      instrumentation.pushFeatureUsage(hostParams.rails_session_id, {"iosChromeSetContextError" : `set context max retries reached`}, () => {});
      HubLogger.exceptionLogger("iosChromeSetContextError max retries reached for " + hostParams.rails_session_id + " retries: " + retries, '', '', '');
      callback();
      return;
    }
    const sel = new SeleniumClient(hostParams);
    const webContext = await getWebContextName(hostParams, request);
    await sel.setContext(webContext);
    callback();
    return;
  } catch (err) {
    HubLogger.exceptionLogger("setContextIOSChrome failed for " + hostParams.rails_session_id + " Error: " + (err ? err.toString() : "undefined"), '', "", err);
    exports.PingZombie({
      "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
      "session_id": hostParams.rails_session_id,
      "data": err ? err.toString().substring(0, 1024) : "undefined",
      "kind": "aut_ios_chrome_contextset_failed",
      "user_id": hostParams.user_id,
      "os": hostParams.os
    });
    await exports.sleep(2000);
    await setContextIOSChrome(hostParams, request, retries + 1, callback);
  }
};

exports.setContextIOSChrome = setContextIOSChrome;

const getWebContextName = async (hostParams, _) => {
  const sel = new SeleniumClient(hostParams);
  const contexts = await sel.getContexts();

  const view = contexts.value.find(function(el) {
    return el.match(/WEBVIEW.*/);
  });
  if (view != undefined) {
    return view;
  } else {
    throw new Error(`Did not find webview`);
  }
};

exports.s3LogFormat = function(type, time_ts, logdata){
  return time_ts + " " + type + " " + logdata;
};

exports.getDate = function() {
  const date = new Date();
  //Format: yyyy-mm-dd HH:MM:ss
  return ((1900 + date.getYear()) + "-" + (date.getMonth() + 1) + "-" + date.getDate() + " " + date.getHours() + ":" + date.getMinutes() + ":" + date.getSeconds() + ":" + date.getMilliseconds());
};

/**
 * checks for logs of "EXCEPTION" category and returns true if logs are exception logs
 */
exports.isExceptionLog = (category) => {
  return isNotUndefined(category) && category === 'EXCEPTION';
};

exports.instrumentRedisCommand = (command, elapsedTime = 0) => {
  constants.pushToHootHootRegistry['redisCommandCount'] = constants.pushToHootHootRegistry['redisCommandCount'] || {};
  const count = (constants.pushToHootHootRegistry['redisCommandCount'][command] || 0) + 1;
  constants.pushToHootHootRegistry['redisCommandCount'][command] = count;
  constants.pushToHootHootRegistry['redisCommandRate'] = constants.pushToHootHootRegistry['redisCommandRate'] || {};
  constants.pushToHootHootRegistry['redisCommandRate'][command] = ((constants.pushToHootHootRegistry['redisCommandRate'][command] || 0) + elapsedTime) / count;
};

exports.skipJarDeleteExperiment = (os) => {
  return (constants.ENABLE_EXPERIMENT_SKIP_JAR_DELETE && os.match(/win/i));
};

// utility method takes a string and an object for templating
// string needs to have variables in the form <VARIABLE>
// where object will be having variable and its actual value
exports.stringTemplating = (message, metaData) => {
  let finalMessage = message;
  Object.keys(metaData).forEach((key) => {
    finalMessage = finalMessage.replace(`<${key}>`, metaData[key]);
  });
  return finalMessage;
};

exports.getDashboardFireCmdMessage = (opt) => {
  let message;
  const { error_type, error_meta_data, firecmd_error_message, error_from } = opt;
  if(error_type) {
    if(constants.firecmd_app_installation_kinds.includes(error_type) && error_from == 'user_error'){
      message = ("Could not start a session. Something went wrong with app launch. " + firecmd_error_message).slice(0,550);
    }
    else {
      message = constants.firecmd_custom_exceptions[error_type];
      if(constants.exceptions_needing_templating.includes(error_type) && error_meta_data) message = exports.stringTemplating(message, error_meta_data);
    }
  }
  message = message || constants.firecmd_custom_exceptions["unknown_exception"];
  return message;
};

exports.getOsVersion = (deviceName) => {
  let osVersion = undefined;
  try{
    if(isString(deviceName)) {
      osVersion = parseFloat(deviceName.split('-')[1]);
    }
  } catch(e) {
    HubLogger.miscLogger('getOsVersion', `Failed to fetch os version from ${deviceName}`, LL.DEBUG);
  }

  return isNaN(osVersion) ? undefined : osVersion;
};

exports.isMobileCommand = (url, req_data, command) => {
  let mobileExecute = false;
  try{
    mobileExecute = url.match(/\/execute/) && req_data.includes(command);
  } catch(e) {
    HubLogger.miscLogger('isMobileCommand', `Failed to determine if it is a mobile command ${url} ${req_data} Error: ${e}`, LL.INFO);
  }

  return !!mobileExecute;
};

exports.generateRailsCustomTimeout = (options, retries) => {
  let customRailsTimeOut = 0;

  if(options.post_params.isAppAutomate && options.post_params.desiredCapabilities) {
    try {
      let desiredCaps = JSON.parse(decodeURIComponent(options.post_params.desiredCapabilities));
      let reserveDevice = desiredCaps["browserstack.reserveDevice"] || desiredCaps["bstack:options"]["reserveDevice"];
      let customRetries = constants.RESERVED_TERMINAL_QUEUEING.RESERVED_TERMINAL_CUSTOM_RETRIES;
      let customTime = constants.RESERVED_TERMINAL_QUEUEING.RESERVED_TERMINAL_MAX_WAIT_TIME / customRetries;
      if(retries <= customRetries && reserveDevice.toString().toLowerCase() == "true") customRailsTimeOut = customTime;
    } catch(err) {
      customRailsTimeOut = 0;
    }
  }

  if (!options.post_params.isAppAutomate && retries && options.reason && options.reason === "nta") {
    try {
      const exponentialRetryDelay = constants.NTA_RETRY_EXPONENTIAL_DELAY.DEFAULT_NTA_RETRY_DELAY;
      const exponentialRetryDelaySize = exponentialRetryDelay.length;
      const defaultRetryDelay = constants.NTA_RETRY_EXPONENTIAL_DELAY.NTA_RETRY_DEFAULT_DELAY;
      customRailsTimeOut = defaultRetryDelay;

      // The retry delay default in case of NTA is [10, 25, 30]
      // If the attempt is greater than the size of the delay the delay is default of 50 seconds
      // If the attempt is lower than the size of the delay the delay would be one of the [10, 25, 30]
      // thus resulting in 1st attempt at 10, 2nd attempt at 25 and 3rd attempt at 30 seconds, 4th and all subsequent attempts at 50 seconds

      if (retries > exponentialRetryDelaySize) {
        customRailsTimeOut = defaultRetryDelay;
      } else {
        customRailsTimeOut = exponentialRetryDelay[(retries - 1)];
      }
    } catch(err) {
      customRailsTimeOut = 0;
    }
  }

  return customRailsTimeOut;
};

exports.startUpFailureCallbackHandler = (options, device, appTesting, messageMap) => {
  if(options.sessionId) {
    var message = 'BROWSER START-UP FAILURE';
    if(device)
      message = options.realMobile ? ( appTesting ? '[BROWSERSTACK_COULD_NOT_LAUNCH_APP] The app could not be launched': 'COULD NOT START MOBILE BROWSER') : 'COULD NOT BOOT EMULATOR';
    if (appTesting) {
      let keyValMessage = this.nestedKeyValue(messageMap['value'], ['message']);
      if(isString(keyValMessage)) {
        messageMap['value']['message'] = this.getCustomMessageForError(options, keyValMessage);
      }
    }
    HubLogger.addStopToRawLogs(options, options.sessionId, message, 1, true, null, JSON.stringify(messageMap));
  }
};

exports.escapeCharactersForJs = (inputData) => {
  // escapes ' \ characters
  return inputData.replace(/['\\]/g, '\\$&');
};

exports.setClientConnectionSocketsCount = (request, keyObject, key) => {
  if(request && keyObject && key && !(request.is_app_automate_session) && this.isDefined(request.headers["x-connection-requests"]) && parseInt(request.headers["x-connection-requests"]) >= 2){
    var clientConnectionSocketsCount = keyObject.clientConnectionSocketsCount;
    if(clientConnectionSocketsCount <= 1 ){
      clientConnectionSocketsCount = parseInt(request.headers["x-connection-requests"]);
      constants.global_registry[key].clientConnectionSocketsCount = clientConnectionSocketsCount;
    }
  }
};

exports.sleep = millis => (
  new Promise(((resolve) => {
    setTimeout(() => {
      resolve();
    }, millis);
  }))
);

//Delete request from trackset in redis
exports.unregisterDroppedRailsRequestFromTrackSet = async function(trackRequestID, isAppAutomateSession) {
  const trackRequestTag = isAppAutomateSession ?
  constants.railsPipeline.redisAppAutomateTrackTokenHash.queueTrackRequestsSetTag :
  constants.railsPipeline.redisAutomateTrackTokenHash.queueTrackRequestsSetTag;
  const logTag = isAppAutomateSession ? 'appAutomate' : 'automate';
  let elapsedTime = 0;
  const current = new Date();
  redisClientSecond.unregisterRailsRequest([trackRequestTag, trackRequestID])
    .then(unregisterResponse => {
      const wasRequestTracked = unregisterResponse[0] === 1;
      const trackRequestSetSize = unregisterResponse[1];
      if (wasRequestTracked) workerHubRailsPipelineManager.removeFromWorkerHubRailsPipelineCounter();
      HubLogger.miscLogger(`unregisterTrackSet_${trackRequestID}`, `[${logTag}] Response: wasTracked: ${wasRequestTracked} trackRequestSetSize: ${trackRequestSetSize}`, LL.INFO);
      elapsedTime = Date.now() - current;
      exports.instrumentRedisCommand('unregisterRailsRequest', elapsedTime);
    });
};

exports.getDeviceName = function (keyObject) {
  const isMobile = ['ios', 'android'].includes(keyObject.os.toLowerCase());
  if (isMobile) {
    return keyObject.deviceName ? keyObject.deviceName.split('-')[0].trim() : null;
  }
  return `${keyObject.os}_${keyObject.os_version}_${keyObject.browser}_${keyObject.browser_version}`.toLowerCase();
};
