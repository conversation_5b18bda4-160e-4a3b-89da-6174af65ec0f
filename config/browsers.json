{"yandex": [{"version": "14.12", "command": "start \"\" \"C:\\Program Files\\YandexBrowser\\Application\\browser.exe\"", "release": "stable", "dirty": "00", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/Yandex.exe", "command_mac": "/Applications/Yandex.app/Contents/MacOS/Yandex", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/software/Yandex.dmg", "full_version": "14.12.2125.9579", "disk_space": "200 MB"}], "chrome": [{"version": "14.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\14.0.835.163\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 14\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/chrome_installer_14.0.835.163.exe", "command_mac": "/Applications/GoogleChrome14.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData14", "full_version": "14.0.835.163", "disk_space": "110 MB"}, {"version": "18.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\18.0.1025.152\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 18\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/chrome_installer_18.0.1025.152.exe", "command_mac": "/Applications/GoogleChrome18.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData18", "full_version": "18.0.1025.152", "disk_space": "110 MB"}, {"version": "17.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\17.0.963.66\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 17\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/chrome_installer_17.0.963.66.exe", "command_mac": "/Applications/GoogleChrome17.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData17", "full_version": "17.0.963.66", "disk_space": "110 MB"}, {"version": "16.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\16.0.912.77\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 16\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/chrome_installer_16.0.912.77.exe", "command_mac": "/Applications/GoogleChrome16.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData16", "full_version": "16.0.912.77", "disk_space": "110 MB"}, {"version": "15.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\15.0.874.121\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 15\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/chrome_installer_15.0.874.121.exe", "full_version": "15.0.874.121", "disk_space": "110 MB"}, {"version": "20.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\20.0.1132.57\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 20\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/20.0.1132.57_chrome_installer.exe", "command_mac": "/Applications/GoogleChrome20.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData20", "full_version": "20.0.1132.57", "disk_space": "115 MB"}, {"version": "19.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\19.0.1084.56\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 19\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/chrome_installer_19.0.1084.56.exe", "command_mac": "/Applications/GoogleChrome19.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData19", "full_version": "19.0.1084.56", "disk_space": "110 MB"}, {"version": "24.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\24.0.1312.57\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 24\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/24.0.1312.57_chrome_installer.exe", "command_mac": "/Applications/GoogleChrome24.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData24", "full_version": "24.0.1312.57", "disk_space": "125 MB"}, {"version": "23.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\23.0.1271.64\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 23\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/x64/23.0.1271.64_chrome_installer.exe", "command_mac": "/Applications/GoogleChrome23.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData23", "full_version": "23.0.1271.64", "disk_space": "145 MB"}, {"version": "22.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\22.0.1229.96\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 22\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/x64/22.0.1229.96_chrome_installer.exe", "command_mac": "/Applications/GoogleChrome22.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData22", "full_version": "22.0.1229.96", "disk_space": "120 MB"}, {"version": "21.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\21.0.1180.89\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 21\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/21.0.1180.89_chrome_installer.exe", "command_mac": "/Applications/GoogleChrome21.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData21", "full_version": "21.0.1180.89", "disk_space": "115 MB"}, {"version": "25.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\25.0.1364.160\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 25\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/25.0.1364.160_chrome_installer.exe", "command_mac": "/Applications/GoogleChrome25.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData25", "full_version": "25.0.1364.160", "disk_space": "125 MB"}, {"version": "26.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\26.0.1410.43\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 26\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/26.0.1410.43_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome26.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData26", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/26.0.1410.43_chrome_installer.dmg", "full_version": "26.0.1410.43", "disk_space": "130 MB"}, {"version": "27.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\27.0.1453.110\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 27\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/27.0.1453.110_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome27.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData27", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/27.0.1453.110_chrome_installer.dmg", "full_version": "27.0.1453.110", "disk_space": "130 MB"}, {"version": "28.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\28.0.1500.72\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 28\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/28.0.1500.72_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome28.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData28", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/28.0.1500.72_chrome_installer.dmg", "full_version": "28.0.1500.72", "disk_space": "140 MB"}, {"version": "29.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\29.0.1547.62\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/29.0.1547.62_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome29.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData29", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/29.0.1547.62_chrome_installer.dmg", "full_version": "29.0.1547.62", "disk_space": "140 MB"}, {"version": "30.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\30.0.1599.101\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 30\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/30.0.1599.101_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome30.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData30", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/30.0.1599.101_chrome_installer.dmg", "full_version": "30.0.1599.101", "disk_space": "140 MB"}, {"version": "31.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\31.0.1650.57\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 31\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/31.0.1650.57_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome31.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData31", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/31.0.1650.57_chrome_installer.dmg", "full_version": "31.0.1650.57", "disk_space": "145 MB"}, {"version": "32.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\32.0.1700.76\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 32\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/32.0.1700.76_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome32.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData32", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/32.0.1700.76_chrome_installer.dmg", "full_version": "32.0.1700.76", "disk_space": "145 MB"}, {"version": "33.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\33.0.1750.117\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 33\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/33.0.1750.117_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome33.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData33", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/33.0.1750.117_chrome_installer.dmg", "full_version": "33.0.1750.117", "disk_space": "150 MB"}, {"version": "34.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\34.0.1847.116\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 34\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/34.0.1847.116_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome34.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData34", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/34.0.1847.116_chrome_installer.dmg", "full_version": "34.0.1847.116", "disk_space": "150 MB"}, {"version": "35.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\35.0.1916.114\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 35\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/35.0.1916.114_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome35.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData35", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/35.0.1916.114_chrome_installer.dmg", "full_version": "35.0.1916.114", "disk_space": "150 MB"}, {"version": "36.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\36.0.1985.125\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 36\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/36.0.1985.125_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome36.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData36", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/36.0.1985.125_chrome_installer.dmg", "full_version": "36.0.1985.125", "disk_space": "160 MB"}, {"version": "37.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\37.0.2062.102\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 37\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/37.0.2062.102_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome37.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData37", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/37.0.2062.102_chrome_installer.dmg", "full_version": "37.0.2062.102", "disk_space": "160 MB"}, {"version": "38.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\38.0.2125.101\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 38\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/38.0.2125.101_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome38.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData38", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/38.0.2125.101_chrome_installer.dmg", "full_version": "38.0.2125.101", "disk_space": "160 MB"}, {"version": "39.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\39.0.2171.65\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 39\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/39.0.2171.65_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome39.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData39", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/39.0.2171.65_chrome_installer.dmg", "full_version": "39.0.2171.65", "disk_space": "180 MB"}, {"version": "40.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\40.0.2214.93\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 40\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/40.0.2214.93_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome40.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData40", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/40.0.2214.93_chrome_installer.dmg", "full_version": "40.0.2214.93", "disk_space": "180 MB"}, {"version": "41.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\41.0.2272.89\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 41\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/41.0.2272.89_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome41.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData41", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/41.0.2272.89_chrome_installer.dmg", "full_version": "41.0.2272.89", "disk_space": "180 MB"}, {"version": "42.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\42.0.2311.135\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 42\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/42.0.2311.135_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome42.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData42", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/42.0.2311.135_chrome_installer.dmg", "full_version": "42.0.2311.135", "disk_space": "180 MB"}, {"version": "43.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\43.0.2357.65\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 43\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/43.0.2357.65_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome43.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData43", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/43.0.2357.65_chrome_installer.dmg", "full_version": "43.0.2357.65", "disk_space": "180 MB"}, {"version": "44.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\44.0.2403.89\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 44\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/44.0.2403.89_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome44.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData44", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/44.0.2403.89_chrome_installer.dmg", "full_version": "44.0.2403.89", "disk_space": "180 MB"}, {"version": "45.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\45.0.2454.85\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 45\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/45.0.2454.85_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome45.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData45", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/45.0.2454.85_chrome_installer.dmg", "full_version": "45.0.2454.85", "disk_space": "180 MB"}, {"version": "46.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\46.0.2490.71\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 46\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/46.0.2490.71_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome46.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData46", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/46.0.2490.71_chrome_installer.dmg", "full_version": "46.0.2490.71", "disk_space": "180 MB"}, {"version": "47.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\47.0.2526.73\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 47\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/47.0.2526.73_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome47.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData47", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/47.0.2526.73_chrome_installer.dmg", "full_version": "47.0.2526.73", "disk_space": "180 MB"}, {"version": "48.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\48.0.2564.97\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 48\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/48.0.2564.97_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome48.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData48", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/48.0.2564.97_chrome_installer.dmg", "full_version": "48.0.2564.97", "disk_space": "180 MB"}, {"version": "49.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\49.0.2623.75\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 49\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/49.0.2623.75_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome49.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData49", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/49.0.2623.75_chrome_installer.dmg", "full_version": "49.0.2623.75", "disk_space": "180 MB"}, {"version": "50.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\50.0.2661.75\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 50\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/50.0.2661.75_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome50.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData50", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/50.0.2661.75_chrome_installer.dmg", "full_version": "50.0.2661.75", "disk_space": "180 MB"}, {"version": "51.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\51.0.2704.63\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 51\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/51.0.2704.63_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome51.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData51", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/51.0.2704.79_chrome_installer.dmg", "full_version": "51.0.2704.63", "disk_space": "180 MB"}, {"version": "52.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\52.0.2743.82\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 52\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/52.0.2743.82_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome52.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData52", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/52.0.2743.82_chrome_installer.dmg", "full_version": "52.0.2743.82", "disk_space": "180 MB"}, {"version": "53.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\53.0.2785.89\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 53\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/53.0.2785.89_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome53.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData53", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/53.0.2785.89_chrome_installer.dmg", "full_version": "53.0.2785.89", "disk_space": "180 MB"}, {"version": "54.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\54.0.2840.59\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 54\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/54.0.2840.59_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome54.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData54", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/54.0.2840.59_chrome_installer.dmg", "full_version": "54.0.2840.59", "disk_space": "180 MB"}, {"version": "55.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\55.0.2883.75\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 55\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/55.0.2883.75_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome55.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData55", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/55.0.2883.75_chrome_installer.dmg", "full_version": "55.0.2883.75", "disk_space": "180 MB"}, {"version": "56.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\56.0.2924.76\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 56\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/56.0.2924.76_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome56.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData56", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/56.0.2924.76_chrome_installer.dmg", "full_version": "56.0.2924.76", "disk_space": "180 MB"}, {"version": "57.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\57.0.2987.98\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 57\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/57.0.2987.98_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome57.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData57", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/57.0.2987.98_chrome_installer.dmg", "full_version": "57.0.2987.98", "disk_space": "180 MB"}, {"version": "58.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\58.0.3029.81\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 58\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/58.0.3029.81_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome58.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData58", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/58.0.3029.81_chrome_installer.dmg", "full_version": "58.0.3029.81", "disk_space": "180 MB"}, {"version": "59.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\59.0.3071.86\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 59\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/59.0.3071.86_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome59.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData59", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/59.0.3071.86_chrome_installer.dmg", "full_version": "59.0.3071.86", "disk_space": "180 MB"}, {"version": "60.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\60.0.3112.78\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 60\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/60.0.3112.78_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome60.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData60", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/60.0.3112.78_chrome_installer.dmg", "full_version": "60.0.3112.78", "disk_space": "180 MB"}, {"version": "61.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\61.0.3163.79\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 61\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/61.0.3163.79_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome61.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData61", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/61.0.3163.79_chrome_installer.dmg", "full_version": "61.0.3163.79", "disk_space": "180 MB"}, {"version": "62.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\62.0.3202.62\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 62\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/62.0.3202.62_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome62.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData62", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/62.0.3202.62_chrome_installer.dmg", "full_version": "62.0.3202.62", "disk_space": "180 MB"}, {"version": "63.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\63.0.3239.84\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 63\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/63.0.3239.84_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome63.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData63", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/63.0.3239.84_chrome_installer.dmg", "full_version": "63.0.3239.84", "disk_space": "180 MB"}, {"version": "64.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\64.0.3282.119\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 64\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/64.0.3282.119_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome64.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData64", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/64.0.3282.119_chrome_installer.dmg", "full_version": "64.0.3282.119", "disk_space": "180 MB"}, {"version": "65.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\65.0.3325.146\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 65\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/65.0.3325.146_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome65.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData65", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/65.0.3325.146_chrome_installer.dmg", "full_version": "65.0.3325.146", "disk_space": "180 MB"}, {"version": "66.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\66.0.3359.117\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 66\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/66.0.3359.117_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome66.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData66", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/66.0.3359.117_chrome_installer.dmg", "full_version": "66.0.3359.117", "disk_space": "180 MB"}, {"version": "67.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\67.0.3396.62\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 67\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/67.0.3396.62_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome67.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData67", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/67.0.3396.62_chrome_installer.dmg", "full_version": "67.0.3396.62", "disk_space": "180 MB"}, {"version": "68.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\68.0.3440.70\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 68\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/68.0.3440.70_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome68.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData68", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/68.0.3440.70_chrome_installer.dmg", "full_version": "68.0.3440.70", "disk_space": "180 MB"}, {"version": "69.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\69.0.3497.81\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 69\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/69.0.3497.81_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome69.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData69", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/69.0.3497.81_chrome_installer.dmg", "full_version": "69.0.3497.81", "disk_space": "180 MB"}, {"version": "70.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\70.0.3538.67\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 70\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/70.0.3538.67_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome70.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData70", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/70.0.3538.67_chrome_installer.dmg", "full_version": "70.0.3538.67", "disk_space": "180 MB"}, {"version": "71.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\71.0.3578.80\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 71\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/71.0.3578.80_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome71.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData71", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/71.0.3578.80_chrome_installer.dmg", "full_version": "71.0.3578.80", "disk_space": "200 MB"}, {"version": "72.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\72.0.3626.81\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 72\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/72.0.3626.81_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome72.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData72", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/72.0.3626.81_chrome_installer.dmg", "full_version": "72.0.3626.81", "disk_space": "200 MB"}, {"version": "73.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\73.0.3683.75\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 73\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/73.0.3683.75_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome73.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData73", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/73.0.3683.75_chrome_installer.dmg", "full_version": "73.0.3683.75", "disk_space": "200 MB"}, {"version": "74.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\74.0.3729.108\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 74\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/74.0.3729.108_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome74.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData74", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/74.0.3729.108_chrome_installer.dmg", "full_version": "74.0.3729.108", "disk_space": "200 MB"}, {"version": "75.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\75.0.3770.80\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 75\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/75.0.3770.80_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome75.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData75", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/75.0.3770.80_chrome_installer.dmg", "full_version": "75.0.3770.80", "disk_space": "200 MB"}, {"version": "76.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\76.0.3809.87\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 76\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/76.0.3809.87_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome76.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData76", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/76.0.3809.87_chrome_installer.dmg", "full_version": "76.0.3809.87", "disk_space": "210 MB"}, {"version": "77.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\77.0.3865.75\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 77\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/77.0.3865.75_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome77.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData77", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/77.0.3865.75_chrome_installer.dmg", "full_version": "77.0.3865.75", "disk_space": "210 MB"}, {"version": "78.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\78.0.3904.70\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 78\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/78.0.3904.70_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome78.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData78", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/78.0.3904.70_chrome_installer.dmg", "full_version": "78.0.3904.70", "disk_space": "210 MB"}, {"version": "79.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\79.0.3945.36\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 79\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/79.0.3945.36_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome79.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData79", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/79.0.3945.79_chrome_installer.dmg", "full_version": "79.0.3945.36", "disk_space": "225 MB"}, {"version": "80.0", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\80.0.3987.132\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 80\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/80.0.3987.132_chrome_installer.exe", "dirty": "00", "command_mac": "/Applications/GoogleChrome80.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData80", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/80.0.3987.132_chrome_installer.dmg", "full_version": "80.0.3987.132", "disk_space": "225 MB"}, {"version": "81.0", "release": "stable", "dirty": "00", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/81.0.4044.122_chrome_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/81.0.4044.122_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\81.0.4044.122\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 81\"", "command_mac": "/Applications/GoogleChrome81.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData81", "full_version": "81.0.4044.122", "disk_space": "225 MB"}, {"version": "136.0", "release": "beta", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/136.0.7103.17_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/136.0.7103.17_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\beta136.0.7103.17\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data beta136\"", "command_mac": "/Applications/GoogleChromebeta136.app/Contents/MacOS/Google\\ Chrome\\ Beta --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --user-data-dir=/Users/<USER>/.chrome/UserDatabeta136", "full_version": "136.0.7103.17", "disk_space": "480 MB"}, {"version": "137.0", "release": "beta", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/137.0.7151.6_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/137.0.7151.6_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\beta137.0.7151.6\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data beta137\"", "command_mac": "/Applications/GoogleChromebeta137.app/Contents/MacOS/Google\\ Chrome\\ Beta --start-maximized --no-default-browser-check --no-first-run --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --user-data-dir=/Users/<USER>/.chrome/UserDatabeta137", "full_version": "137.0.7151.6", "disk_space": "490 MB"}, {"version": "137.0", "release": "dev", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/137.0.7106.2_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/137.0.7106.2_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\dev137.0.7106.2\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data dev137\"", "command_mac": "/Applications/GoogleChromedev137.app/Contents/MacOS/Google\\ Chrome\\ Dev --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --user-data-dir=/Users/<USER>/.chrome/UserDatadev137", "full_version": "137.0.7106.2", "disk_space": "540 MB"}, {"version": "138.0", "release": "dev", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/138.0.7153.0_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/138.0.7153.0_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\dev138.0.7153.0\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data dev138\"", "command_mac": "/Applications/GoogleChromedev138.app/Contents/MacOS/Google\\ Chrome\\ Dev --start-maximized --no-default-browser-check --no-first-run --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --user-data-dir=/Users/<USER>/.chrome/UserDatadev138", "full_version": "138.0.7153.0", "disk_space": "500 MB"}, {"version": "83.0", "release": "stable", "dirty": "00", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/83.0.4103.61_chrome_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/83.0.4103.61_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\83.0.4103.61\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 83\"", "command_mac": "/Applications/GoogleChrome83.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData83", "full_version": "83.0.4103.61", "disk_space": "225 MB"}, {"version": "84.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/84.0.4147.89_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/84.0.4147.89_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\84.0.4147.89\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 84\"", "command_mac": "/Applications/GoogleChrome84.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData84", "full_version": "84.0.4147.89", "disk_space": "225 MB"}, {"version": "85.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/85.0.4183.83_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/85.0.4183.83_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\85.0.4183.83\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 85\"", "command_mac": "/Applications/GoogleChrome85.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData85", "full_version": "85.0.4183.83", "disk_space": "225 MB"}, {"version": "86.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/86.0.4240.75_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/86.0.4240.75_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\86.0.4240.75\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 86\"", "command_mac": "/Applications/GoogleChrome86.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData86", "full_version": "86.0.4240.75", "disk_space": "225 MB"}, {"version": "87.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/87.0.4280.66_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/87.0.4280.66_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\87.0.4280.66\\chrome.exe\" --start-maximized --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 87\"", "command_mac": "/Applications/GoogleChrome87.app/Contents/MacOS/Google\\ Chrome --start-maximized --user-data-dir=/Users/<USER>/.chrome/UserData87", "full_version": "87.0.4280.66", "disk_space": "250 MB"}, {"version": "88.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/88.0.4324.150_chrome_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/88.0.4324.150_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\88.0.4324.150\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 88\"", "command_mac": "/Applications/GoogleChrome88.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData88", "full_version": "88.0.4324.150", "disk_space": "250 MB"}, {"version": "89.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/89.0.4389.90_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/89.0.4389.90_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\89.0.4389.90\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 89\"", "command_mac": "/Applications/GoogleChrome89.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData89", "full_version": "89.0.4389.90", "disk_space": "250 MB"}, {"version": "90.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/90.0.4430.72_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/90.0.4430.72_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\90.0.4430.72\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 90\"", "command_mac": "/Applications/GoogleChrome90.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData90", "full_version": "90.0.4430.72", "disk_space": "250 MB"}, {"version": "91.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/91.0.4472.106_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/91.0.4472.106_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\91.0.4472.106\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 91\"", "command_mac": "/Applications/GoogleChrome91.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData91", "full_version": "91.0.4472.106", "disk_space": "250 MB"}, {"version": "92.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/92.0.4515.107_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/92.0.4515.107_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\92.0.4515.107\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 92\"", "command_mac": "/Applications/GoogleChrome92.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData92", "full_version": "92.0.4515.107", "disk_space": "250 MB"}, {"version": "93.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/93.0.4577.63_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/93.0.4577.63_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\93.0.4577.63\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 93\"", "command_mac": "/Applications/GoogleChrome93.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData93", "full_version": "93.0.4577.63", "disk_space": "250 MB"}, {"version": "94.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/94.0.4606.71_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/94.0.4606.71_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\94.0.4606.71\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 94\"", "command_mac": "/Applications/GoogleChrome94.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData94", "full_version": "94.0.4606.71", "disk_space": "260 MB"}, {"version": "95.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/95.0.4638.54_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/95.0.4638.54_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\95.0.4638.54\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 95\"", "command_mac": "/Applications/GoogleChrome95.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData95", "full_version": "95.0.4638.54", "disk_space": "260 MB"}, {"version": "96.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/96.0.4664.110_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/96.0.4664.110_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\96.0.4664.110\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 96\"", "command_mac": "/Applications/GoogleChrome96.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData96", "full_version": "96.0.4664.110", "disk_space": "260 MB"}, {"version": "97.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/97.0.4692.71_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/97.0.4692.71_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\97.0.4692.71\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 97\"", "command_mac": "/Applications/GoogleChrome97.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData97", "full_version": "97.0.4692.71", "disk_space": "260 MB"}, {"version": "98.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/98.0.4758.80_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/98.0.4758.80_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\98.0.4758.80\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 98\"", "command_mac": "/Applications/GoogleChrome98.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData98", "full_version": "98.0.4758.80", "disk_space": "260 MB"}, {"version": "99.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/99.0.4844.51_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/99.0.4844.51_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\99.0.4844.51\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 99\"", "command_mac": "/Applications/GoogleChrome99.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData99", "full_version": "99.0.4844.51", "disk_space": "450 MB"}, {"version": "100.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/100.0.4896.127_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/100.0.4896.127_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\100.0.4896.127\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 100\"", "command_mac": "/Applications/GoogleChrome100.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData100", "full_version": "100.0.4896.127", "disk_space": "450 MB"}, {"version": "101.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/101.0.4951.41_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/101.0.4951.41_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\101.0.4951.41\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 101\"", "command_mac": "/Applications/GoogleChrome101.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData101", "full_version": "101.0.4951.41", "disk_space": "455 MB"}, {"version": "102.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/102.0.5005.63_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/102.0.5005.63_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\102.0.5005.63\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 102\"", "command_mac": "/Applications/GoogleChrome102.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData102", "full_version": "102.0.5005.63", "disk_space": "455 MB"}, {"version": "103.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/103.0.5060.114_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/103.0.5060.114_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\103.0.5060.114\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 103\"", "command_mac": "/Applications/GoogleChrome103.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData103", "full_version": "103.0.5060.114", "disk_space": "455 MB"}, {"version": "104.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/104.0.5112.81_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/104.0.5112.81_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\104.0.5112.81\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 104\"", "command_mac": "/Applications/GoogleChrome104.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData104", "full_version": "104.0.5112.81", "disk_space": "475 MB"}, {"version": "105.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/105.0.5195.54_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/105.0.5195.54_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\105.0.5195.54\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 105\"", "command_mac": "/Applications/GoogleChrome105.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData105", "full_version": "105.0.5195.54", "disk_space": "475 MB"}, {"version": "106.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/106.0.5249.62_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/106.0.5249.62_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\106.0.5249.62\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 106\"", "command_mac": "/Applications/GoogleChrome106.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData106", "full_version": "106.0.5249.62", "disk_space": "475 MB"}, {"version": "107.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/107.0.5304.63_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/107.0.5304.63_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\107.0.5304.63\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 107\"", "command_mac": "/Applications/GoogleChrome107.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData107", "full_version": "107.0.5304.63", "disk_space": "475 MB"}, {"version": "108.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/108.0.5359.72_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/108.0.5359.72_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\108.0.5359.72\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 108\"", "command_mac": "/Applications/GoogleChrome108.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData108", "full_version": "108.0.5359.72", "disk_space": "475 MB"}, {"version": "109.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/109.0.5414.75_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/109.0.5414.75_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\109.0.5414.75\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 109\"", "command_mac": "/Applications/GoogleChrome109.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData109", "full_version": "109.0.5414.75", "disk_space": "475 MB"}, {"version": "110.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/110.0.5481.78_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/110.0.5481.78_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\110.0.5481.78\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 110\"", "command_mac": "/Applications/GoogleChrome110.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData110", "full_version": "110.0.5481.78", "disk_space": "475 MB"}, {"version": "111.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/111.0.5563.65_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/111.0.5563.65_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\111.0.5563.65\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 111\"", "command_mac": "/Applications/GoogleChrome111.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData111", "full_version": "111.0.5563.65", "disk_space": "475 MB"}, {"version": "112.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/112.0.5615.50_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/112.0.5615.50_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\112.0.5615.50\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 112\"", "command_mac": "/Applications/GoogleChrome112.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData112", "full_version": "112.0.5615.50", "disk_space": "475 MB"}, {"version": "113.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/113.0.5672.64_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/113.0.5672.64_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\113.0.5672.64\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 113\"", "command_mac": "/Applications/GoogleChrome113.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData113", "full_version": "113.0.5672.64", "disk_space": "475 MB"}, {"version": "114.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/114.0.5735.91_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/114.0.5735.91_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\114.0.5735.91\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 114\"", "command_mac": "/Applications/GoogleChrome114.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData114", "full_version": "114.0.5735.91", "disk_space": "475 MB"}, {"version": "115.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/115.0.5790.99_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/115.0.5790.99_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\115.0.5790.99\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 115\"", "command_mac": "/Applications/GoogleChrome115.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData115", "full_version": "115.0.5790.99", "disk_space": "475 MB"}, {"version": "116.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/116.0.5845.97_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/116.0.5845.97_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\116.0.5845.97\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 116\"", "command_mac": "/Applications/GoogleChrome116.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData116", "full_version": "116.0.5845.97", "disk_space": "475 MB"}, {"version": "117.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/117.0.5938.63_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/117.0.5938.63_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\117.0.5938.63\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 117\"", "command_mac": "/Applications/GoogleChrome117.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData117", "full_version": "117.0.5938.63", "disk_space": "475 MB"}, {"version": "118.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/118.0.5993.71_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/118.0.5993.71_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\118.0.5993.71\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 118\"", "command_mac": "/Applications/GoogleChrome118.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData118", "full_version": "118.0.5993.71", "disk_space": "475 MB"}, {"version": "119.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/119.0.6045.106_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/119.0.6045.106_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\119.0.6045.106\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 119\"", "command_mac": "/Applications/GoogleChrome119.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData119", "full_version": "119.0.6045.106", "disk_space": "475 MB"}, {"version": "120.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/120.0.6099.63_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/120.0.6099.63_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\120.0.6099.63\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 120\"", "command_mac": "/Applications/GoogleChrome120.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData120", "full_version": "120.0.6099.63", "disk_space": "475 MB"}, {"version": "121.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/121.0.6167.86_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/121.0.6167.86_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\121.0.6167.86\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 121\"", "command_mac": "/Applications/GoogleChrome121.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData121", "full_version": "121.0.6167.86", "disk_space": "475 MB"}, {"version": "122.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/122.0.6261.58_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/122.0.6261.58_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\122.0.6261.58\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 122\"", "command_mac": "/Applications/GoogleChrome122.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData122", "full_version": "122.0.6261.58", "disk_space": "475 MB"}, {"version": "123.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/123.0.6312.59_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/123.0.6312.59_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\123.0.6312.59\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 123\"", "command_mac": "/Applications/GoogleChrome123.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData123", "full_version": "123.0.6312.59", "disk_space": "475 MB"}, {"version": "124.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/124.0.6367.61_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/124.0.6367.61_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\124.0.6367.61\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 124\"", "command_mac": "/Applications/GoogleChrome124.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData124", "full_version": "124.0.6367.61", "disk_space": "475 MB"}, {"version": "125.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/125.0.6422.61_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/125.0.6422.61_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\125.0.6422.61\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 125\"", "command_mac": "/Applications/GoogleChrome125.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData125", "full_version": "125.0.6422.61", "disk_space": "475 MB"}, {"version": "126.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/126.0.6478.57_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/126.0.6478.57_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\126.0.6478.57\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 126\"", "command_mac": "/Applications/GoogleChrome126.app/Contents/MacOS/Google\\ Chrome --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData126", "full_version": "126.0.6478.57", "disk_space": "475 MB"}, {"version": "127.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/127.0.6533.73_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/127.0.6533.73_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\127.0.6533.73\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 127\"", "command_mac": "/Applications/GoogleChrome127.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData127", "full_version": "127.0.6533.73", "disk_space": "475 MB"}, {"version": "128.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/128.0.6613.85_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/128.0.6613.85_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\128.0.6613.85\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 128\"", "command_mac": "/Applications/GoogleChrome128.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData128", "full_version": "128.0.6613.85", "disk_space": "475 MB"}, {"version": "129.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/129.0.6668.59_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/129.0.6668.59_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\129.0.6668.59\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 129\"", "command_mac": "/Applications/GoogleChrome129.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData129", "full_version": "129.0.6668.59", "disk_space": "475 MB"}, {"version": "130.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/130.0.6723.59_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/130.0.6723.59_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\130.0.6723.59\\chrome.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 130\"", "command_mac": "/Applications/GoogleChrome130.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData130", "full_version": "130.0.6723.59", "disk_space": "475 MB"}, {"version": "131.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/131.0.6778.70_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/131.0.6778.70_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\131.0.6778.70\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 131\"", "command_mac": "/Applications/GoogleChrome131.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData131", "full_version": "131.0.6778.70", "disk_space": "475 MB"}, {"version": "132.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/132.0.6834.84_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/132.0.6834.84_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\132.0.6834.84\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 132\"", "command_mac": "/Applications/GoogleChrome132.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData132", "full_version": "132.0.6834.84", "disk_space": "475 MB"}, {"version": "133.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/133.0.6943.54_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/133.0.6943.54_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\133.0.6943.54\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 133\"", "command_mac": "/Applications/GoogleChrome133.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData133", "full_version": "133.0.6943.54", "disk_space": "475 MB"}, {"version": "134.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/134.0.6998.36_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/134.0.6998.36_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\134.0.6998.36\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 134\"", "command_mac": "/Applications/GoogleChrome134.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData134", "full_version": "134.0.6998.36", "disk_space": "475 MB"}, {"version": "135.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/135.0.7049.42_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/135.0.7049.42_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\135.0.7049.42\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 135\"", "command_mac": "/Applications/GoogleChrome135.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData135", "full_version": "135.0.7049.42", "disk_space": "475 MB"}, {"version": "136.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/chrome/136.0.7103.49_chrome_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/chrome/136.0.7103.49_chrome_installer.dmg", "command": "start \"\" \"c:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.49\\chrome.exe\" --start-maximized --no-default-browser-check --no-first-run --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\user_folder\\hello\\settings_folder\\Google\\Chrome\\User Data 136\"", "command_mac": "/Applications/GoogleChrome136.app/Contents/MacOS/Google\\ Chrome --start-maximized --disable-search-engine-choice-screen --disable-features=PrivacySandboxSettings4 --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.chrome/UserData136", "full_version": "136.0.7103.49", "disk_space": "475 MB"}], "ie": [{"version": "6.0", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "6.0"}, {"version": "7.0", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "7.0"}, {"version": "8.0", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "8.0"}, {"version": "9.0", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "9.0"}, {"version": "10.0", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "10.0"}, {"version": "10.0 Desktop", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "10.0 Desktop"}, {"version": "11.0", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "11.0.9600.16663"}, {"version": "11.0 Desktop", "command": "start \"\" /MAX \"C:\\Program Files\\Internet Explorer\\iexplore.exe\"", "release": "stable", "full_version": "11.0.9600.16663"}], "edge": [{"version": "15.0", "command": "C:\\windows\\explorer.exe microsoft-edge:", "command_desktop": "start microsoft-edge:", "release": "stable", "full_version": "15.15063"}, {"version": "16.0", "command": "C:\\windows\\explorer.exe microsoft-edge:", "command_desktop": "start microsoft-edge:", "release": "stable", "full_version": "16.16299"}, {"version": "17.0", "command": "C:\\windows\\explorer.exe microsoft-edge:", "command_desktop": "start microsoft-edge:", "release": "stable", "full_version": "17.17134"}, {"version": "18.0", "command": "C:\\windows\\explorer.exe microsoft-edge:", "command_desktop": "start microsoft-edge:", "release": "stable", "full_version": "18.18362"}, {"version": "80.0", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\80.0.361.57\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 80\"", "release": "stable", "s3_path_win10_d17": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/80.0.361.57_edge_installer_win10_d17.zip", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/80.0.361.57_edge_installer.exe", "dirty": "00", "command_mac": "/Applications/Edge80.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData80", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/80.0.361.57_edge_installer.pkg", "full_version": "80.0.361.57", "disk_space": "350 MB"}, {"version": "81.0", "release": "stable", "dirty": "00", "s3_path_win10_d17": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/81.0.416.53_edge_installer_win10_d17.zip", "s3_path_win10": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/win10/81.0.416.53_edge_installer.exe", "s3_path_win8.1": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/win8.1/81.0.416.53_edge_installer.exe", "s3_path_win8": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/win8/81.0.416.53_edge_installer.exe", "s3_path_win7": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/win7/81.0.416.53_edge_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/81.0.416.53_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\81.0.416.53\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 81\"", "command_mac": "/Applications/Edge81.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData81", "full_version": "81.0.416.53", "disk_space": "350 MB"}, {"version": "136.0", "release": "beta", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/136.0.3240.8_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/136.0.3240.8_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\beta136.0.3240.8\\msedge.exe\" --start-maximized --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data beta136\"", "command_mac": "/Applications/Edgebeta136.app/Contents/MacOS/Microsoft\\ Edge\\ Beta --start-maximized --no-first-run --user-data-dir=/Users/<USER>/.edge/UserDatabeta136", "full_version": "136.0.3240.8", "disk_space": "750 MB"}, {"version": "137.0", "release": "beta", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/137.0.3296.16_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/137.0.3296.16_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\beta137.0.3296.16\\msedge.exe\" --start-maximized --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data beta137\"", "command_mac": "/Applications/Edgebeta137.app/Contents/MacOS/Microsoft\\ Edge\\ Beta --start-maximized --no-first-run --user-data-dir=/Users/<USER>/.edge/UserDatabeta137", "full_version": "137.0.3296.16", "disk_space": "750 MB"}, {"version": "138.0", "release": "dev", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/138.0.3309.1_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/138.0.3309.1_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\dev138.0.3309.1\\msedge.exe\" --start-maximized --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data dev138\"", "command_mac": "/Applications/Edgedev138.app/Contents/MacOS/Microsoft\\ Edge\\ Dev --start-maximized --no-first-run --user-data-dir=/Users/<USER>/.edge/UserDatadev138", "full_version": "138.0.3309.1", "disk_space": "750 MB"}, {"version": "137.0", "release": "dev", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/137.0.3255.0_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/137.0.3255.0_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\dev137.0.3255.0\\msedge.exe\" --start-maximized --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data dev137\"", "command_mac": "/Applications/Edgedev137.app/Contents/MacOS/Microsoft\\ Edge\\ Dev --start-maximized --no-first-run --user-data-dir=/Users/<USER>/.edge/UserDatadev137", "full_version": "137.0.3255.0", "disk_space": "750 MB"}, {"version": "83.0", "release": "stable", "dirty": "00", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/83.0.478.37_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/83.0.478.37_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\83.0.478.37\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 83\"", "command_mac": "/Applications/Edge83.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData83", "full_version": "83.0.478.37", "disk_space": "360 MB"}, {"version": "84.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/84.0.522.40_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/84.0.522.40_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\84.0.522.40\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 84\"", "command_mac": "/Applications/Edge84.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData84", "full_version": "84.0.522.40", "disk_space": "330 MB"}, {"version": "85.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/85.0.564.41_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/85.0.564.41_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\85.0.564.41\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 85\"", "command_mac": "/Applications/Edge85.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData85", "full_version": "85.0.564.41", "disk_space": "340 MB"}, {"version": "86.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/86.0.622.38_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/86.0.622.38_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\86.0.622.38\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 86\"", "command_mac": "/Applications/Edge86.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData86", "full_version": "86.0.622.38", "disk_space": "390 MB"}, {"version": "87.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/87.0.664.41_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/87.0.664.41_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\87.0.664.41\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 87\"", "command_mac": "/Applications/Edge87.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData87", "full_version": "87.0.664.41", "disk_space": "405 MB"}, {"version": "88.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/88.0.705.50_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/88.0.705.50_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\88.0.705.50\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 88\"", "command_mac": "/Applications/Edge88.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData88", "full_version": "88.0.705.50", "disk_space": "410 MB"}, {"version": "89.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/89.0.774.45_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/89.0.774.45_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\89.0.774.45\\msedge.exe\" --start-maximized --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 89\"", "command_mac": "/Applications/Edge89.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --user-data-dir=/Users/<USER>/.edge/UserData89", "full_version": "89.0.774.45", "disk_space": "325 MB"}, {"version": "90.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/90.0.818.39_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/90.0.818.39_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\90.0.818.39\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 90\"", "command_mac": "/Applications/Edge90.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.edge/UserData90", "full_version": "90.0.818.39", "disk_space": "320 MB"}, {"version": "91.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/91.0.864.37_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/91.0.864.37_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\91.0.864.37\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 91\"", "command_mac": "/Applications/Edge91.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.edge/UserData91", "full_version": "91.0.864.37", "disk_space": "630 MB"}, {"version": "92.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/92.0.902.55_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/92.0.902.55_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\92.0.902.55\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 92\"", "command_mac": "/Applications/Edge92.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.edge/UserData92", "full_version": "92.0.902.55", "disk_space": "580 MB"}, {"version": "93.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/93.0.961.38_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/93.0.961.38_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\93.0.961.38\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 93\"", "command_mac": "/Applications/Edge93.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir=/Users/<USER>/.edge/UserData93", "full_version": "93.0.961.38", "disk_space": "595 MB"}, {"version": "94.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/94.0.992.31_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/94.0.992.31_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\94.0.992.31\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tue, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 94\"", "command_mac": "/Applications/Edge94.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData94", "full_version": "94.0.992.31", "disk_space": "600 MB"}, {"version": "95.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/95.0.1020.30_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/95.0.1020.30_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\95.0.1020.30\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 95\"", "command_mac": "/Applications/Edge95.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData95", "full_version": "95.0.1020.30", "disk_space": "640 MB"}, {"version": "96.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/96.0.1054.34_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/96.0.1054.34_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\96.0.1054.34\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 96\"", "command_mac": "/Applications/Edge96.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData96", "full_version": "96.0.1054.34", "disk_space": "640 MB"}, {"version": "97.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/97.0.1072.55_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/97.0.1072.55_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\97.0.1072.55\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 97\"", "command_mac": "/Applications/Edge97.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData97", "full_version": "97.0.1072.55", "disk_space": "650 MB"}, {"version": "98.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/98.0.1108.43_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/98.0.1108.43_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\98.0.1108.43\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 98\"", "command_mac": "/Applications/Edge98.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData98", "full_version": "98.0.1108.43", "disk_space": "640 MB"}, {"version": "99.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/99.0.1150.36_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/99.0.1150.36_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\99.0.1150.36\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 99\"", "command_mac": "/Applications/Edge99.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData99", "full_version": "99.0.1150.36", "disk_space": "640 MB"}, {"version": "100.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/100.0.1185.29_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/100.0.1185.29_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\100.0.1185.29\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 100\"", "command_mac": "/Applications/Edge100.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData100", "full_version": "100.0.1185.29", "disk_space": "645 MB"}, {"version": "101.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/101.0.1210.32_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/101.0.1210.32_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\101.0.1210.32\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 101\"", "command_mac": "/Applications/Edge101.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData101", "full_version": "101.0.1210.32", "disk_space": "660 MB"}, {"version": "102.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/102.0.1245.33_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/102.0.1245.33_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\102.0.1245.33\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 102\"", "command_mac": "/Applications/Edge102.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData102", "full_version": "102.0.1245.33", "disk_space": "650 MB"}, {"version": "103.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/103.0.1264.37_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/103.0.1264.37_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\103.0.1264.37\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 103\"", "command_mac": "/Applications/Edge103.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData103", "full_version": "103.0.1264.37", "disk_space": "670 MB"}, {"version": "104.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/104.0.1293.47_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/104.0.1293.47_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\104.0.1293.47\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 104\"", "command_mac": "/Applications/Edge104.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData104", "full_version": "104.0.1293.47", "disk_space": "710 MB"}, {"version": "105.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/105.0.1343.25_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/105.0.1343.25_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\105.0.1343.25\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 105\"", "command_mac": "/Applications/Edge105.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData105", "full_version": "105.0.1343.25", "disk_space": "715 MB"}, {"version": "106.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/106.0.1370.37_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/106.0.1370.37_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\106.0.1370.37\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 106\"", "command_mac": "/Applications/Edge106.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData106", "full_version": "106.0.1370.37", "disk_space": "715 MB"}, {"version": "107.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/107.0.1418.24_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/107.0.1418.24_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\107.0.1418.24\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 107\"", "command_mac": "/Applications/Edge107.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData107", "full_version": "107.0.1418.24", "disk_space": "730 MB"}, {"version": "108.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/108.0.1462.42_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/108.0.1462.42_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\108.0.1462.42\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 108\"", "command_mac": "/Applications/Edge108.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData108", "full_version": "108.0.1462.42", "disk_space": "750 MB"}, {"version": "109.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/109.0.1518.49_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/109.0.1518.49_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\109.0.1518.49\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 109\"", "command_mac": "/Applications/Edge109.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData109", "full_version": "109.0.1518.49", "disk_space": "750 MB"}, {"version": "110.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/110.0.1587.41_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/110.0.1587.41_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\110.0.1587.41\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 110\"", "command_mac": "/Applications/Edge110.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData110", "full_version": "110.0.1587.41", "disk_space": "750 MB"}, {"version": "111.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/111.0.1661.41_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/111.0.1661.41_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\111.0.1661.41\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 111\"", "command_mac": "/Applications/Edge111.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData111", "full_version": "111.0.1661.41", "disk_space": "750 MB"}, {"version": "112.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/112.0.1722.34_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/112.0.1722.34_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\112.0.1722.34\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 112\"", "command_mac": "/Applications/Edge112.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData112", "full_version": "112.0.1722.34", "disk_space": "750 MB"}, {"version": "113.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/113.0.1774.35_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/113.0.1774.35_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\113.0.1774.35\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 113\"", "command_mac": "/Applications/Edge113.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData113", "full_version": "113.0.1774.35", "disk_space": "750 MB"}, {"version": "114.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/114.0.1823.37_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/114.0.1823.37_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\114.0.1823.37\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 114\"", "command_mac": "/Applications/Edge114.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData114", "full_version": "114.0.1823.37", "disk_space": "750 MB"}, {"version": "115.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/115.0.1901.183_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/115.0.1901.183_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\115.0.1901.183\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 115\"", "command_mac": "/Applications/Edge115.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData115", "full_version": "115.0.1901.183", "disk_space": "750 MB"}, {"version": "116.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/116.0.1938.54_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/116.0.1938.54_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\116.0.1938.54\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 116\"", "command_mac": "/Applications/Edge116.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData116", "full_version": "116.0.1938.54", "disk_space": "750 MB"}, {"version": "117.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/117.0.2045.31_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/117.0.2045.31_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\117.0.2045.31\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tue, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 117\"", "command_mac": "/Applications/Edge117.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData117", "full_version": "117.0.2045.31", "disk_space": "750 MB"}, {"version": "118.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/118.0.2088.46_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/118.0.2088.46_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\118.0.2088.46\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 118\"", "command_mac": "/Applications/Edge118.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData118", "full_version": "118.0.2088.46", "disk_space": "750 MB"}, {"version": "119.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/119.0.2151.44_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/119.0.2151.44_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\119.0.2151.44\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 119\"", "command_mac": "/Applications/Edge119.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData119", "full_version": "119.0.2151.44", "disk_space": "750 MB"}, {"version": "120.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/120.0.2210.61_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/120.0.2210.61_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\120.0.2210.61\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 120\"", "command_mac": "/Applications/Edge120.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData120", "full_version": "120.0.2210.61", "disk_space": "750 MB"}, {"version": "121.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/121.0.2277.83_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/121.0.2277.83_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\121.0.2277.83\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 121\"", "command_mac": "/Applications/Edge121.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData121", "full_version": "121.0.2277.83", "disk_space": "750 MB"}, {"version": "122.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/122.0.2365.59_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/122.0.2365.59_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\122.0.2365.59\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 122\"", "command_mac": "/Applications/Edge122.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData122", "full_version": "122.0.2365.59", "disk_space": "750 MB"}, {"version": "123.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/123.0.2420.53_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/123.0.2420.53_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\123.0.2420.53\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tue, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 123\"", "command_mac": "/Applications/Edge123.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData123", "full_version": "123.0.2420.53", "disk_space": "750 MB"}, {"version": "124.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/124.0.2478.51_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/124.0.2478.51_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\124.0.2478.51\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 124\"", "command_mac": "/Applications/Edge124.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData124", "full_version": "124.0.2478.51", "disk_space": "750 MB"}, {"version": "125.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/125.0.2535.67_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/125.0.2535.67_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\125.0.2535.67\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 125\"", "command_mac": "/Applications/Edge125.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData125", "full_version": "125.0.2535.67", "disk_space": "750 MB"}, {"version": "126.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/126.0.2592.56_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/126.0.2592.56_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\126.0.2592.56\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tue, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 126\"", "command_mac": "/Applications/Edge126.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData126", "full_version": "126.0.2592.56", "disk_space": "750 MB"}, {"version": "127.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/127.0.2651.74_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/127.0.2651.74_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\127.0.2651.74\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 127\"", "command_mac": "/Applications/Edge127.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData127", "full_version": "127.0.2651.74", "disk_space": "750 MB"}, {"version": "128.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/128.0.2739.42_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/128.0.2739.42_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\128.0.2739.42\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 128\"", "command_mac": "/Applications/Edge128.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData128", "full_version": "128.0.2739.42", "disk_space": "750 MB"}, {"version": "129.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/129.0.2792.65_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/129.0.2792.65_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\129.0.2792.65\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tue, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 129\"", "command_mac": "/Applications/Edge129.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData129", "full_version": "129.0.2792.65", "disk_space": "750 MB"}, {"version": "130.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/130.0.2849.46_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/130.0.2849.46_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\130.0.2849.46\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 130\"", "command_mac": "/Applications/Edge130.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData130", "full_version": "130.0.2849.46", "disk_space": "750 MB"}, {"version": "131.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/131.0.2903.51_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/131.0.2903.51_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\131.0.2903.51\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 131\"", "command_mac": "/Applications/Edge131.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData131", "full_version": "131.0.2903.51", "disk_space": "750 MB"}, {"version": "132.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/132.0.2957.115_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/132.0.2957.115_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\132.0.2957.115\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 132\"", "command_mac": "/Applications/Edge132.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData132", "full_version": "132.0.2957.115", "disk_space": "750 MB"}, {"version": "133.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/133.0.3065.59_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/133.0.3065.59_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\133.0.3065.59\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 133\"", "command_mac": "/Applications/Edge133.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData133", "full_version": "133.0.3065.59", "disk_space": "750 MB"}, {"version": "134.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/134.0.3124.51_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/134.0.3124.51_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\134.0.3124.51\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tu<PERSON>, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 134\"", "command_mac": "/Applications/Edge134.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData134", "full_version": "134.0.3124.51", "disk_space": "750 MB"}, {"version": "135.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/135.0.3179.54_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/135.0.3179.54_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\135.0.3179.54\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tue, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 135\"", "command_mac": "/Applications/Edge135.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData135", "full_version": "135.0.3179.54", "disk_space": "750 MB"}, {"version": "136.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/edge/136.0.3240.50_edge_installer.msi", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/edge/136.0.3240.50_edge_installer.pkg", "command": "start \"\" \"C:\\Program Files\\Microsoft\\Edge\\Application\\136.0.3240.50\\msedge.exe\" --start-maximized --simulate-outdated-no-au=\"Tue, 31 Dec 2099 23:59:59 GMT\" --no-first-run --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Local\\Chromium\\User Data 136\"", "command_mac": "/Applications/Edge136.app/Contents/MacOS/Microsoft\\ Edge --start-maximized --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --no-first-run --user-data-dir=/Users/<USER>/.edge/UserData136", "full_version": "136.0.3240.50", "disk_space": "750 MB"}], "flash": [{"version_ie": "32.0.0.238", "version_non_ie": "32.0.0.238", "version_mac": "32.0.0.238"}], "firefox": [{"version": "4.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 4.0\\firefox.exe\" -P firefox4", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+4.0.1.exe", "command_mac": "/Applications/firefox\\ 4.0.app/Contents/MacOS/firefox-bin -P firefox4", "full_version": "4.0.1", "disk_space": "80 MB"}, {"version": "3.6", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 3.6\\firefox.exe\" -P firefox3.6", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+3.6.17.exe", "command_mac": "/Applications/firefox\\ 3.6.app/Contents/MacOS/firefox-bin -P firefox3.6", "full_version": "3.6.17", "disk_space": "60 MB"}, {"version": "3.5", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 3.5\\firefox.exe\" -P firefox3.5", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+3.5.19.exe", "full_version": "3.5.19", "disk_space": "80 MB"}, {"version": "3.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 3.0\\firefox.exe\" -P firefox3", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+3.0.18.exe", "full_version": "3.0.18", "disk_space": "80 MB"}, {"version": "8.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 8.0\\firefox.exe\" -P firefox8", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+8.0.1.exe", "command_mac": "/Applications/firefox\\ 8.0.app/Contents/MacOS/firefox-bin -P firefox8", "full_version": "8.0.1", "disk_space": "80 MB"}, {"version": "7.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 7.0\\firefox.exe\" -P firefox7", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+7.0.exe", "command_mac": "/Applications/firefox\\ 7.0.app/Contents/MacOS/firefox-bin -P firefox7", "full_version": "7.0", "disk_space": "80 MB"}, {"version": "6.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 6.0\\firefox.exe\" -P firefox6", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+6.0.exe", "command_mac": "/Applications/firefox\\ 6.0.app/Contents/MacOS/firefox-bin -P firefox6", "full_version": "6.0", "disk_space": "80 MB"}, {"version": "5.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 5.0\\firefox.exe\" -P firefox5", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+5.0.1.exe", "command_mac": "/Applications/firefox\\ 5.0.app/Contents/MacOS/firefox-bin -P firefox5", "full_version": "5.0.1", "disk_space": "80 MB"}, {"version": "9.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 9.0\\firefox.exe\" -P firefox9", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+9.0.1.exe", "command_mac": "/Applications/firefox\\ 9.0.app/Contents/MacOS/firefox-bin -P firefox9", "full_version": "9.0.1", "disk_space": "80 MB"}, {"version": "12.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 12.0\\firefox.exe\" -P firefox12", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+12.0.exe", "command_mac": "/Applications/firefox\\ 12.0.app/Contents/MacOS/firefox-bin -P firefox12", "full_version": "12.0", "disk_space": "90 MB"}, {"version": "11.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 11.0\\firefox.exe\" -P firefox11", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+11.0.exe", "command_mac": "/Applications/firefox\\ 11.0.app/Contents/MacOS/firefox-bin -P firefox11", "full_version": "11.0", "disk_space": "90 MB"}, {"version": "10.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 10.0\\firefox.exe\" -P firefox10", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+10.0.exe", "command_mac": "/Applications/firefox\\ 10.0.app/Contents/MacOS/firefox-bin -P firefox10", "full_version": "10.0", "disk_space": "90 MB"}, {"version": "16.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 16.0\\firefox.exe\" -P firefox16", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/x64/Firefox_Setup_16.0.2.exe", "command_mac": "/Applications/firefox\\ 16.0.app/Contents/MacOS/firefox-bin -P firefox16", "full_version": "16.0.2", "disk_space": "90 MB"}, {"version": "15.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 15.0\\firefox.exe\" -P firefox15", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+15.0.exe", "command_mac": "/Applications/firefox\\ 15.0.app/Contents/MacOS/firefox-bin -P firefox15", "full_version": "15.0", "disk_space": "90 MB"}, {"version": "14.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 14.0\\firefox.exe\" -P firefox14", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+14.0.1.exe", "command_mac": "/Applications/firefox\\ 14.0.app/Contents/MacOS/firefox-bin -P firefox14", "full_version": "14.0.1", "disk_space": "90 MB"}, {"version": "13.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 13.0\\firefox.exe\" -P firefox13", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+13.0.exe", "command_mac": "/Applications/firefox\\ 13.0.app/Contents/MacOS/firefox-bin -P firefox13", "full_version": "13.0", "disk_space": "90 MB"}, {"version": "19.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 19.0\\firefox.exe\" -P firefox19", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+19.0.2.exe", "command_mac": "/Applications/firefox\\ 19.0.app/Contents/MacOS/firefox-bin -P firefox19", "full_version": "19.0.2", "disk_space": "90 MB"}, {"version": "18.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 18.0\\firefox.exe\" -P firefox18", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+18.0.2.exe", "command_mac": "/Applications/firefox\\ 18.0.app/Contents/MacOS/firefox-bin -P firefox18", "full_version": "18.0.2", "disk_space": "90 MB"}, {"version": "17.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 17.0\\firefox.exe\" -P firefox17", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+17.0.1.exe", "command_mac": "/Applications/firefox\\ 17.0.app/Contents/MacOS/firefox-bin -P firefox17", "full_version": "17.0.1", "disk_space": "90 MB"}, {"version": "20.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 20.0\\firefox.exe\" -P firefox20", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+20.0.1.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 20.0.app/Contents/MacOS/firefox-bin -P firefox20", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+20.0.1.dmg", "full_version": "20.0.1", "disk_space": "90 MB"}, {"version": "21.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 21.0\\firefox.exe\" -P firefox21", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+21.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 21.0.app/Contents/MacOS/firefox-bin -P firefox21", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+21.0.dmg", "full_version": "21.0", "disk_space": "95 MB"}, {"version": "22.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 22.0\\firefox.exe\" -P firefox22", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+22.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 22.0.app/Contents/MacOS/firefox-bin -P firefox22", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+22.0.dmg", "full_version": "22.0", "disk_space": "95 MB"}, {"version": "23.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 23.0\\firefox.exe\" -P firefox23", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+23.0.1.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 23.0.app/Contents/MacOS/firefox-bin -P firefox23", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+23.0.1.dmg", "full_version": "23.0.1", "disk_space": "100 MB"}, {"version": "24.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 24.0\\firefox.exe\" -P firefox24", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+24.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 24.0.app/Contents/MacOS/firefox-bin -P firefox24", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+24.0.dmg", "full_version": "24.0", "disk_space": "100 MB"}, {"version": "25.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 25.0\\firefox.exe\" -P firefox25", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+25.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 25.0.app/Contents/MacOS/firefox-bin -P firefox25", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+25.0.dmg", "full_version": "25.0", "disk_space": "110 MB"}, {"version": "26.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 26.0\\firefox.exe\" -P firefox26", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+26.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 26.0.app/Contents/MacOS/firefox-bin -P firefox26", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+26.0.dmg", "full_version": "26.0", "disk_space": "110 MB"}, {"version": "27.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 27.0\\firefox.exe\" -P firefox27", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+27.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 27.0.app/Contents/MacOS/firefox-bin -P firefox27", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+27.0.dmg", "full_version": "27.0", "disk_space": "110 MB"}, {"version": "28.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 28.0\\firefox.exe\" -P firefox28", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+28.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 28.0.app/Contents/MacOS/firefox-bin -P firefox28", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+28.0.dmg", "full_version": "28.0", "disk_space": "110 MB"}, {"version": "29.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 29.0\\firefox.exe\" -P firefox29", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+29.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 29.0.app/Contents/MacOS/firefox-bin -P firefox29", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+29.0.dmg", "full_version": "29.0", "disk_space": "140 MB"}, {"version": "30.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 30.0\\firefox.exe\" -P firefox30", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+30.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 30.0.app/Contents/MacOS/firefox-bin -P firefox30", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+30.0.dmg", "full_version": "30.0", "disk_space": "140 MB"}, {"version": "31.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 31.0\\firefox.exe\" -P firefox31", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+31.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 31.0.app/Contents/MacOS/firefox-bin -P firefox31", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+31.0.dmg", "full_version": "31.0", "disk_space": "145 MB"}, {"version": "32.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 32.0\\firefox.exe\" -P firefox32", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Firefox+Setup+32.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 32.0.app/Contents/MacOS/firefox-bin -P firefox32", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Firefox+32.0.dmg", "full_version": "32.0", "disk_space": "150 MB"}, {"version": "33.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 33.0\\firefox.exe\" -P firefox33", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox+Setup+33.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 33.0.app/Contents/MacOS/firefox-bin -P firefox33", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox+33.0.dmg", "full_version": "33.0", "disk_space": "150 MB"}, {"version": "34.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 34.0\\firefox.exe\" -P firefox34", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox+Setup+34.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 34.0.app/Contents/MacOS/firefox-bin -P firefox34", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox+34.0.dmg", "full_version": "34.0", "disk_space": "155 MB"}, {"version": "35.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 35.0\\firefox.exe\" -P firefox35", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_35.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 35.0.app/Contents/MacOS/firefox-bin -P firefox35", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_35.0.dmg", "full_version": "35.0", "disk_space": "160 MB"}, {"version": "36.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 36.0\\firefox.exe\" -P firefox36", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_36.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 36.0.app/Contents/MacOS/firefox-bin -P firefox36", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_36.0.dmg", "full_version": "36.0", "disk_space": "165 MB"}, {"version": "37.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 37.0\\firefox.exe\" -P firefox37", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_37.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 37.0.app/Contents/MacOS/firefox-bin -P firefox37", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_37.0.dmg", "full_version": "37.0", "disk_space": "165 MB"}, {"version": "38.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 38.0\\firefox.exe\" -P firefox38", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_38.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 38.0.app/Contents/MacOS/firefox-bin -P firefox38", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_38.0.dmg", "full_version": "38.0", "disk_space": "165 MB"}, {"version": "39.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 39.0\\firefox.exe\" -P firefox39", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_39.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 39.0.app/Contents/MacOS/firefox-bin -P firefox39", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_39.0.dmg", "full_version": "39.0", "disk_space": "170 MB"}, {"version": "40.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 40.0\\firefox.exe\" -P firefox40", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_40.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 40.0.app/Contents/MacOS/firefox-bin -P firefox40", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_40.0.dmg", "full_version": "40.0", "disk_space": "175 MB"}, {"version": "41.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 41.0\\firefox.exe\" -P firefox41", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_41.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 41.0.app/Contents/MacOS/firefox-bin -P firefox41", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_41.0.dmg", "full_version": "41.0", "disk_space": "175 MB"}, {"version": "42.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 42.0\\firefox.exe\" -P firefox42", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_42.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 42.0.app/Contents/MacOS/firefox-bin -P firefox42", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_42.0.dmg", "full_version": "42.0", "disk_space": "175 MB"}, {"version": "43.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 43.0\\firefox.exe\" -P firefox43", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_43.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 43.0.app/Contents/MacOS/firefox-bin -P firefox43", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_43.0.dmg", "full_version": "43.0", "disk_space": "180 MB"}, {"version": "44.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 44.0\\firefox.exe\" -P firefox44", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_44.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 44.0.app/Contents/MacOS/firefox-bin -P firefox44", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_44.0.dmg", "full_version": "44.0", "disk_space": "180 MB"}, {"version": "45.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 45.0\\firefox.exe\" -P firefox45", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_45.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 45.0.app/Contents/MacOS/firefox-bin -P firefox45", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_45.0.dmg", "full_version": "45.0", "disk_space": "190 MB"}, {"version": "46.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 46.0\\firefox.exe\" -P firefox46", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/Firefox_Setup_46.0.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 46.0.app/Contents/MacOS/firefox-bin -P firefox46", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/Firefox_46.0.dmg", "full_version": "46.0", "disk_space": "190 MB"}, {"version": "47.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 47.0\\firefox.exe\" -P firefox47", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/47.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 47.0.app/Contents/MacOS/firefox-bin -P firefox47", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/47.0_firefox_installer.dmg", "full_version": "47.0.1", "disk_space": "195 MB"}, {"version": "48.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 48.0\\firefox.exe\" -P firefox48", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/48.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 48.0.app/Contents/MacOS/firefox-bin -P firefox48", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/48.0_firefox_installer.dmg", "full_version": "48.0", "disk_space": "190 MB"}, {"version": "49.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 49.0\\firefox.exe\" -P firefox49", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/49.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 49.0.app/Contents/MacOS/firefox-bin -P firefox49", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/49.0_firefox_installer.dmg", "full_version": "49.0", "disk_space": "190 MB"}, {"version": "50.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 50.0\\firefox.exe\" -P firefox50", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/50.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 50.0.app/Contents/MacOS/firefox-bin -P firefox50", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/50.0_firefox_installer.dmg", "full_version": "50.0", "disk_space": "190 MB"}, {"version": "51.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 51.0\\firefox.exe\" -P firefox51", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/51.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 51.0.app/Contents/MacOS/firefox-bin -P firefox51", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/51.0_firefox_installer.dmg", "full_version": "51.0", "disk_space": "190 MB"}, {"version": "52.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 52.0\\firefox.exe\" -P firefox52", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/52.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 52.0.app/Contents/MacOS/firefox-bin -P firefox52", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/52.0_firefox_installer.dmg", "full_version": "52.0", "disk_space": "190 MB"}, {"version": "53.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 53.0\\firefox.exe\" -P firefox53", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/53.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 53.0.app/Contents/MacOS/firefox-bin -P firefox53", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/53.0_firefox_installer.dmg", "full_version": "53.0", "disk_space": "130 MB"}, {"version": "54.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 54.0\\firefox.exe\" -P firefox54", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/54.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 54.0.app/Contents/MacOS/firefox-bin -P firefox54", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/54.0_firefox_installer.dmg", "full_version": "54.0", "disk_space": "130 MB"}, {"version": "55.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 55.0\\firefox.exe\" -P firefox55", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/55.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 55.0.app/Contents/MacOS/firefox-bin -P firefox55", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/55.0_firefox_installer.dmg", "full_version": "55.0", "disk_space": "150 MB"}, {"version": "56.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 56.0\\firefox.exe\" -P firefox56", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/56.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 56.0.app/Contents/MacOS/firefox-bin -P firefox56", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/56.0_firefox_installer.dmg", "full_version": "56.0", "disk_space": "160 MB"}, {"version": "57.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 57.0\\firefox.exe\" -P firefox57", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/57.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 57.0.app/Contents/MacOS/firefox-bin -P firefox57", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/57.0_firefox_installer.dmg", "full_version": "57.0", "disk_space": "160 MB"}, {"version": "58.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 58.0\\firefox.exe\" -P firefox58", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/58.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 58.0.app/Contents/MacOS/firefox-bin -P firefox58", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/58.0_firefox_installer.dmg", "full_version": "58.0", "disk_space": "160 MB"}, {"version": "59.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 59.0\\firefox.exe\" -P firefox59", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/59.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 59.0.app/Contents/MacOS/firefox-bin -P firefox59", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/59.0_firefox_installer.dmg", "full_version": "59.0", "disk_space": "160 MB"}, {"version": "60.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 60.0\\firefox.exe\" -P firefox60", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/60.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 60.0.app/Contents/MacOS/firefox-bin -P firefox60", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/60.0_firefox_installer.dmg", "full_version": "60.0", "disk_space": "160 MB"}, {"version": "61.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 61.0\\firefox.exe\" -P firefox61", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/61.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 61.0.app/Contents/MacOS/firefox-bin -P firefox61", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/61.0_firefox_installer.dmg", "full_version": "61.0", "disk_space": "160 MB"}, {"version": "62.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 62.0\\firefox.exe\" -P firefox62", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/62.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 62.0.app/Contents/MacOS/firefox-bin -P firefox62", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/62.0_firefox_installer.dmg", "full_version": "62.0", "disk_space": "160 MB"}, {"version": "63.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 63.0\\firefox.exe\" -P firefox63", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/63.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 63.0.app/Contents/MacOS/firefox-bin -P firefox63", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/63.0_firefox_installer.dmg", "full_version": "63.0", "disk_space": "165 MB"}, {"version": "64.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 64.0\\firefox.exe\" -P firefox64", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/64.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 64.0.app/Contents/MacOS/firefox-bin -P firefox64", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/64.0_firefox_installer.dmg", "full_version": "64.0", "disk_space": "180 MB"}, {"version": "65.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 65.0\\firefox.exe\" -P firefox65", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/65.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 65.0.app/Contents/MacOS/firefox-bin -P firefox65", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/65.0_firefox_installer.dmg", "full_version": "65.0", "disk_space": "185 MB"}, {"version": "66.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 66.0\\firefox.exe\" -P firefox66", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/66.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 66.0.app/Contents/MacOS/firefox-bin -P firefox66", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/66.0_firefox_installer.dmg", "full_version": "66.0", "disk_space": "185 MB"}, {"version": "67.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 67.0\\firefox.exe\" -P firefox67", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/67.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 67.0.app/Contents/MacOS/firefox-bin -P firefox67", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/67.0_firefox_installer.dmg", "full_version": "67.0", "disk_space": "185 MB"}, {"version": "68.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 68.0\\firefox.exe\" -P firefox68", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/68.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 68.0.app/Contents/MacOS/firefox-bin -P firefox68", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/68.0_firefox_installer.dmg", "full_version": "68.0", "disk_space": "190 MB"}, {"version": "69.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 69.0\\firefox.exe\" -P firefox69", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/69.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 69.0.app/Contents/MacOS/firefox-bin -P firefox69", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/69.0_firefox_installer.dmg", "full_version": "69.0", "disk_space": "190 MB"}, {"version": "70.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 70.0\\firefox.exe\" -P firefox70", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/70.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 70.0.app/Contents/MacOS/firefox-bin -P firefox70", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/70.0_firefox_installer.dmg", "full_version": "70.0", "disk_space": "195 MB"}, {"version": "71.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 71.0\\firefox.exe\" -P firefox71", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/71.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 71.0.app/Contents/MacOS/firefox-bin -P firefox71", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/71.0_firefox_installer.dmg", "full_version": "71.0", "disk_space": "195 MB"}, {"version": "72.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 72.0\\firefox.exe\" -P firefox72", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/72.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 72.0.app/Contents/MacOS/firefox-bin -P firefox72", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/72.0_firefox_installer.dmg", "full_version": "72.0", "disk_space": "195 MB"}, {"version": "73.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 73.0\\firefox.exe\" -P firefox73", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/73.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 73.0.app/Contents/MacOS/firefox-bin -P firefox73", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/73.0_firefox_installer.dmg", "full_version": "73.0", "disk_space": "185 MB"}, {"version": "74.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 74.0\\firefox.exe\" -P firefox74", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/74.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 74.0.app/Contents/MacOS/firefox-bin -P firefox74", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/74.0_firefox_installer.dmg", "full_version": "74.0", "disk_space": "180 MB"}, {"version": "75.0", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 75.0\\firefox.exe\" -P firefox75", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/75.0_firefox_installer.exe", "dirty": "00", "command_mac": "/Applications/firefox\\ 75.0.app/Contents/MacOS/firefox-bin -P firefox75", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/75.0_firefox_installer.dmg", "full_version": "75.0", "disk_space": "190 MB"}, {"version": "76.0", "release": "stable", "dirty": "00", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/76.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/76.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 76.0\\firefox.exe\" -P firefox76", "command_mac": "/Applications/firefox\\ 76.0.app/Contents/MacOS/firefox-bin -P firefox76", "full_version": "76.0", "disk_space": "190 MB"}, {"version": "140.0", "release": "beta", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/140.0b1_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/140.0b1_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox beta140.0\\firefox.exe\" -P firefoxbeta", "command_mac": "/Applications/firefox\\ beta140.0.app/Contents/MacOS/firefox -P firefoxbeta", "full_version": "140.0b1", "disk_space": "360 MB"}, {"version": "139.0", "release": "beta", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/139.0b1_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/139.0b1_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox beta139.0\\firefox.exe\" -P firefoxbeta", "command_mac": "/Applications/firefox\\ beta139.0.app/Contents/MacOS/firefox -P firefoxbeta", "full_version": "139.0b1", "disk_space": "360 MB"}, {"version": "77.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/77.01_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/77.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 77.0\\firefox.exe\" -P firefox77", "command_mac": "/Applications/firefox\\ 77.0.app/Contents/MacOS/firefox-bin -P firefox77", "full_version": "77.0", "disk_space": "200 MB"}, {"version": "78.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/78.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/78.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 78.0\\firefox.exe\" -P firefox78", "command_mac": "/Applications/firefox\\ 78.0.app/Contents/MacOS/firefox-bin -P firefox78", "full_version": "78.0", "disk_space": "200 MB"}, {"version": "79.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/79.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/79.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 79.0\\firefox.exe\" -P firefox79", "command_mac": "/Applications/firefox\\ 79.0.app/Contents/MacOS/firefox-bin -P firefox79", "full_version": "79.0", "disk_space": "200 MB"}, {"version": "80.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/80.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/80.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 80.0\\firefox.exe\" -P firefox80", "command_mac": "/Applications/firefox\\ 80.0.app/Contents/MacOS/firefox-bin -P firefox80", "full_version": "80.0", "disk_space": "200 MB"}, {"version": "81.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/81.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/81.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 81.0\\firefox.exe\" -P firefox81", "command_mac": "/Applications/firefox\\ 81.0.app/Contents/MacOS/firefox-bin -P firefox81", "full_version": "81.0", "disk_space": "200 MB"}, {"version": "82.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/82.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/82.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 82.0\\firefox.exe\" -P firefox82", "command_mac": "/Applications/firefox\\ 82.0.app/Contents/MacOS/firefox-bin -P firefox82", "full_version": "82.0", "disk_space": "200 MB"}, {"version": "83.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/83.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/83.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 83.0\\firefox.exe\" -P firefox83", "command_mac": "/Applications/firefox\\ 83.0.app/Contents/MacOS/firefox-bin -P firefox83", "full_version": "83.0", "disk_space": "200 MB"}, {"version": "84.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/84.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/84.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 84.0\\firefox.exe\" -P firefox84", "command_mac": "/Applications/firefox\\ 84.0.app/Contents/MacOS/firefox-bin -P firefox84", "full_version": "84.0", "disk_space": "350 MB"}, {"version": "85.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/85.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/85.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 85.0\\firefox.exe\" -P firefox85", "command_mac": "/Applications/firefox\\ 85.0.app/Contents/MacOS/firefox-bin -P firefox85", "full_version": "85.0", "disk_space": "350 MB"}, {"version": "86.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/86.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/86.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 86.0\\firefox.exe\" -P firefox86", "command_mac": "/Applications/firefox\\ 86.0.app/Contents/MacOS/firefox-bin -P firefox86", "full_version": "86.0", "disk_space": "350 MB"}, {"version": "87.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/87.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/87.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 87.0\\firefox.exe\" -P firefox87", "command_mac": "/Applications/firefox\\ 87.0.app/Contents/MacOS/firefox-bin -P firefox87", "full_version": "87.0", "disk_space": "360 MB"}, {"version": "88.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/88.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/88.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 88.0\\firefox.exe\" -P firefox88", "command_mac": "/Applications/firefox\\ 88.0.app/Contents/MacOS/firefox-bin -P firefox88", "full_version": "88.0", "disk_space": "360 MB"}, {"version": "89.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/89.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/89.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 89.0\\firefox.exe\" -P firefox89", "command_mac": "/Applications/firefox\\ 89.0.app/Contents/MacOS/firefox-bin -P firefox89", "full_version": "89.0", "disk_space": "360 MB"}, {"version": "90.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/90.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/90.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 90.0\\firefox.exe\" -P firefox90", "command_mac": "/Applications/firefox\\ 90.0.app/Contents/MacOS/firefox-bin -P firefox90", "full_version": "90.0", "disk_space": "360 MB"}, {"version": "91.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/91.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/91.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 91.0\\firefox.exe\" -P firefox91", "command_mac": "/Applications/firefox\\ 91.0.app/Contents/MacOS/firefox-bin -P firefox91", "full_version": "91.0", "disk_space": "360 MB"}, {"version": "92.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/92.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/92.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 92.0\\firefox.exe\" -P firefox92", "command_mac": "/Applications/firefox\\ 92.0.app/Contents/MacOS/firefox-bin -P firefox92", "full_version": "92.0", "disk_space": "360 MB"}, {"version": "93.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/93.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/93.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 93.0\\firefox.exe\" -P firefox93", "command_mac": "/Applications/firefox\\ 93.0.app/Contents/MacOS/firefox-bin -P firefox93", "full_version": "93.0", "disk_space": "360 MB"}, {"version": "94.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/94.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/94.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 94.0\\firefox.exe\" -P firefox94", "command_mac": "/Applications/firefox\\ 94.0.app/Contents/MacOS/firefox-bin -P firefox94", "full_version": "94.0", "disk_space": "360 MB"}, {"version": "95.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/95.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/95.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 95.0\\firefox.exe\" -P firefox95", "command_mac": "/Applications/firefox\\ 95.0.app/Contents/MacOS/firefox-bin -P firefox95", "full_version": "95.0", "disk_space": "340 MB"}, {"version": "96.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/96.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/96.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 96.0\\firefox.exe\" -P firefox96", "command_mac": "/Applications/firefox\\ 96.0.app/Contents/MacOS/firefox-bin -P firefox96", "full_version": "96.0", "disk_space": "340 MB"}, {"version": "97.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/97.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/97.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 97.0\\firefox.exe\" -P firefox97", "command_mac": "/Applications/firefox\\ 97.0.app/Contents/MacOS/firefox-bin -P firefox97", "full_version": "97.0", "disk_space": "350 MB"}, {"version": "98.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/98.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/98.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 98.0\\firefox.exe\" -P firefox98", "command_mac": "/Applications/firefox\\ 98.0.app/Contents/MacOS/firefox-bin -P firefox98", "full_version": "98.0", "disk_space": "340 MB"}, {"version": "99.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/99.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/99.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 99.0\\firefox.exe\" -P firefox99", "command_mac": "/Applications/firefox\\ 99.0.app/Contents/MacOS/firefox-bin -P firefox99", "full_version": "99.0", "disk_space": "340 MB"}, {"version": "100.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/100.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/100.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 100.0\\firefox.exe\" -P firefox100", "command_mac": "/Applications/firefox\\ 100.0.app/Contents/MacOS/firefox-bin -P firefox100", "full_version": "100.0", "disk_space": "345 MB"}, {"version": "101.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/101.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/101.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 101.0\\firefox.exe\" -P firefox101", "command_mac": "/Applications/firefox\\ 101.0.app/Contents/MacOS/firefox-bin -P firefox101", "full_version": "101.0", "disk_space": "345 MB"}, {"version": "102.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/102.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/102.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 102.0\\firefox.exe\" -P firefox102", "command_mac": "/Applications/firefox\\ 102.0.app/Contents/MacOS/firefox-bin -P firefox102", "full_version": "102.0", "disk_space": "345 MB"}, {"version": "103.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/103.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/103.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 103.0\\firefox.exe\" -P firefox103", "command_mac": "/Applications/firefox\\ 103.0.app/Contents/MacOS/firefox-bin -P firefox103", "full_version": "103.0", "disk_space": "345 MB"}, {"version": "104.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/104.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/104.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 104.0\\firefox.exe\" -P firefox104", "command_mac": "/Applications/firefox\\ 104.0.app/Contents/MacOS/firefox-bin -P firefox104", "full_version": "104.0", "disk_space": "345 MB"}, {"version": "105.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/105.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/105.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 105.0\\firefox.exe\" -P firefox105", "command_mac": "/Applications/firefox\\ 105.0.app/Contents/MacOS/firefox-bin -P firefox105", "full_version": "105.0", "disk_space": "345 MB"}, {"version": "106.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/106.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/106.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 106.0\\firefox.exe\" -P firefox106", "command_mac": "/Applications/firefox\\ 106.0.app/Contents/MacOS/firefox-bin -P firefox106", "full_version": "106.0", "disk_space": "350 MB"}, {"version": "107.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/107.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/107.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 107.0\\firefox.exe\" -P firefox107", "command_mac": "/Applications/firefox\\ 107.0.app/Contents/MacOS/firefox-bin -P firefox107", "full_version": "107.0", "disk_space": "350 MB"}, {"version": "108.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/108.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/108.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 108.0\\firefox.exe\" -P firefox108", "command_mac": "/Applications/firefox\\ 108.0.app/Contents/MacOS/firefox-bin -P firefox108", "full_version": "108.0", "disk_space": "350 MB"}, {"version": "109.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/109.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/109.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 109.0\\firefox.exe\" -P firefox109", "command_mac": "/Applications/firefox\\ 109.0.app/Contents/MacOS/firefox-bin -P firefox109", "full_version": "109.0", "disk_space": "350 MB"}, {"version": "110.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/110.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/110.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 110.0\\firefox.exe\" -P firefox110", "command_mac": "/Applications/firefox\\ 110.0.app/Contents/MacOS/firefox-bin -P firefox110", "full_version": "110.0", "disk_space": "350 MB"}, {"version": "111.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/111.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/111.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 111.0\\firefox.exe\" -P firefox111", "command_mac": "/Applications/firefox\\ 111.0.app/Contents/MacOS/firefox-bin -P firefox111", "full_version": "111.0", "disk_space": "350 MB"}, {"version": "112.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/112.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/112.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 112.0\\firefox.exe\" -P firefox112", "command_mac": "/Applications/firefox\\ 112.0.app/Contents/MacOS/firefox-bin -P firefox112", "full_version": "112.0", "disk_space": "350 MB"}, {"version": "113.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/113.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/113.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 113.0\\firefox.exe\" -P firefox113", "command_mac": "/Applications/firefox\\ 113.0.app/Contents/MacOS/firefox-bin -P firefox113", "full_version": "113.0", "disk_space": "350 MB"}, {"version": "114.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/114.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/114.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 114.0\\firefox.exe\" -P firefox114", "command_mac": "/Applications/firefox\\ 114.0.app/Contents/MacOS/firefox-bin -P firefox114", "full_version": "114.0", "disk_space": "350 MB"}, {"version": "115.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/115.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/115.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 115.0\\firefox.exe\" -P firefox115", "command_mac": "/Applications/firefox\\ 115.0.app/Contents/MacOS/firefox-bin -P firefox115", "full_version": "115.0", "disk_space": "350 MB"}, {"version": "116.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/116.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/116.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 116.0\\firefox.exe\" -P firefox116", "command_mac": "/Applications/firefox\\ 116.0.app/Contents/MacOS/firefox-bin -P firefox116", "full_version": "116.0", "disk_space": "350 MB"}, {"version": "117.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/117.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/117.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 117.0\\firefox.exe\" -P firefox117", "command_mac": "/Applications/firefox\\ 117.0.app/Contents/MacOS/firefox-bin -P firefox117", "full_version": "117.0", "disk_space": "350 MB"}, {"version": "118.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/118.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/118.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 118.0\\firefox.exe\" -P firefox118", "command_mac": "/Applications/firefox\\ 118.0.app/Contents/MacOS/firefox-bin -P firefox118", "full_version": "118.0", "disk_space": "350 MB"}, {"version": "119.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/119.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/119.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 119.0\\firefox.exe\" -P firefox119", "command_mac": "/Applications/firefox\\ 119.0.app/Contents/MacOS/firefox-bin -P firefox119", "full_version": "119.0", "disk_space": "350 MB"}, {"version": "120.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/120.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/120.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 120.0\\firefox.exe\" -P firefox120", "command_mac": "/Applications/firefox\\ 120.0.app/Contents/MacOS/firefox-bin -P firefox120", "full_version": "120.0", "disk_space": "350 MB"}, {"version": "121.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/121.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/121.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 121.0\\firefox.exe\" -P firefox121", "command_mac": "/Applications/firefox\\ 121.0.app/Contents/MacOS/firefox -P firefox121", "full_version": "121.0", "disk_space": "350 MB"}, {"version": "122.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/122.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/122.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 122.0\\firefox.exe\" -P firefox122", "command_mac": "/Applications/firefox\\ 122.0.app/Contents/MacOS/firefox -P firefox122", "full_version": "122.0", "disk_space": "350 MB"}, {"version": "123.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/123.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/123.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 123.0\\firefox.exe\" -P firefox123", "command_mac": "/Applications/firefox\\ 123.0.app/Contents/MacOS/firefox -P firefox123", "full_version": "123.0", "disk_space": "350 MB"}, {"version": "124.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/124.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/124.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 124.0\\firefox.exe\" -P firefox124", "command_mac": "/Applications/firefox\\ 124.0.app/Contents/MacOS/firefox -P firefox124", "full_version": "124.0", "disk_space": "350 MB"}, {"version": "125.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/125.0.1_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/125.0.1_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 125.0\\firefox.exe\" -P firefox125", "command_mac": "/Applications/firefox\\ 125.0.app/Contents/MacOS/firefox -P firefox125", "full_version": "125.0.1", "disk_space": "350 MB"}, {"version": "126.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/126.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/126.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 126.0\\firefox.exe\" -P firefox126", "command_mac": "/Applications/firefox\\ 126.0.app/Contents/MacOS/firefox -P firefox126", "full_version": "126.0", "disk_space": "350 MB"}, {"version": "127.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/127.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/127.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 127.0\\firefox.exe\" -P firefox127", "command_mac": "/Applications/firefox\\ 127.0.app/Contents/MacOS/firefox -P firefox127", "full_version": "127.0", "disk_space": "350 MB"}, {"version": "128.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/128.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/128.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 128.0\\firefox.exe\" -P firefox128", "command_mac": "/Applications/firefox\\ 128.0.app/Contents/MacOS/firefox -P firefox128", "full_version": "128.0", "disk_space": "350 MB"}, {"version": "129.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/129.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/129.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 129.0\\firefox.exe\" -P firefox129", "command_mac": "/Applications/firefox\\ 129.0.app/Contents/MacOS/firefox -P firefox129", "full_version": "129.0", "disk_space": "350 MB"}, {"version": "130.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/130.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/130.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 130.0\\firefox.exe\" -P firefox130", "command_mac": "/Applications/firefox\\ 130.0.app/Contents/MacOS/firefox -P firefox130", "full_version": "130.0", "disk_space": "350 MB"}, {"version": "131.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/131.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/131.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 131.0\\firefox.exe\" -P firefox131", "command_mac": "/Applications/firefox\\ 131.0.app/Contents/MacOS/firefox -P firefox131", "full_version": "131.0", "disk_space": "350 MB"}, {"version": "132.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/132.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/132.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 132.0\\firefox.exe\" -P firefox132", "command_mac": "/Applications/firefox\\ 132.0.app/Contents/MacOS/firefox -P firefox132", "full_version": "132.0", "disk_space": "350 MB"}, {"version": "133.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/133.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/133.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 133.0\\firefox.exe\" -P firefox133", "command_mac": "/Applications/firefox\\ 133.0.app/Contents/MacOS/firefox -P firefox133", "full_version": "133.0", "disk_space": "350 MB"}, {"version": "134.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/134.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/134.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 134.0\\firefox.exe\" -P firefox134", "command_mac": "/Applications/firefox\\ 134.0.app/Contents/MacOS/firefox -P firefox134", "full_version": "134.0", "disk_space": "350 MB"}, {"version": "135.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/135.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/135.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 135.0\\firefox.exe\" -P firefox135", "command_mac": "/Applications/firefox\\ 135.0.app/Contents/MacOS/firefox -P firefox135", "full_version": "135.0", "disk_space": "350 MB"}, {"version": "136.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/136.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/136.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 136.0\\firefox.exe\" -P firefox136", "command_mac": "/Applications/firefox\\ 136.0.app/Contents/MacOS/firefox -P firefox136", "full_version": "136.0", "disk_space": "350 MB"}, {"version": "137.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/137.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/137.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 137.0\\firefox.exe\" -P firefox137", "command_mac": "/Applications/firefox\\ 137.0.app/Contents/MacOS/firefox -P firefox137", "full_version": "137.0", "disk_space": "350 MB"}, {"version": "138.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/138.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/138.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 138.0\\firefox.exe\" -P firefox138", "command_mac": "/Applications/firefox\\ 138.0.app/Contents/MacOS/firefox -P firefox138", "full_version": "138.0", "disk_space": "350 MB"}, {"version": "139.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/firefox/139.0_firefox_installer.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/firefox/139.0_firefox_installer.dmg", "command": "start \"\" /MAX \"c:\\Program Files\\firefox 139.0\\firefox.exe\" -P firefox139", "command_mac": "/Applications/firefox\\ 139.0.app/Contents/MacOS/firefox -P firefox139", "full_version": "139.0", "disk_space": "350 MB"}], "java": [{"version": "8u181", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/java/java-se.exe", "s3_path_64": "http://s3.amazonaws.com/bs-platform/windows/java/java-se.exe"}], "opera": [{"version": "12.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 12.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1200_int_Setup.exe", "dirty": "00", "command_mac": "/Applications/opera\\ 12.0.app/Contents/MacOS/Opera -newtab", "full_version": "12.0", "disk_space": "475 MB"}, {"version": "12.13", "release": "stable", "dirty": "00", "command_mac": "/Applications/opera\\ Next.app/Contents/MacOS/Opera -newtab -pd /Users/<USER>/.operaprofile\\ 12.13", "full_version": "12.13", "disk_space": "475 MB"}, {"version": "12.12", "release": "stable", "dirty": "00", "command_mac": "/Applications/opera\\ 12.12.app/Contents/MacOS/Opera -newtab -pd /Users/<USER>/.operaprofile\\ 12.12", "full_version": "12.12", "disk_space": "475 MB"}, {"version": "12.1", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 12.1\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/x64/Opera_1210_int_Setup.exe", "dirty": "00", "full_version": "12.1", "disk_space": "475 MB"}, {"version": "12.14", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 12.14\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1214_int_Setup.exe", "dirty": "00", "command_mac": "/Applications/opera\\ 12.14.app/Contents/MacOS/Opera -newtab -pd /Users/<USER>/.operaprofile\\ 12.14", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_12.14_Setup_Intel.dmg", "full_version": "12.14", "disk_space": "475 MB"}, {"version": "10.6", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 10\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1063_int_Setup.exe", "dirty": "00", "full_version": "10.6", "disk_space": "475 MB"}, {"version": "11.1", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 11.1\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1111_int_Setup.exe", "dirty": "00", "command_mac": "/Applications/opera\\ 11.1.app/Contents/MacOS/Opera -newtab", "full_version": "11.1", "disk_space": "475 MB"}, {"version": "11.6", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 11.6\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1162_int_Setup.exe", "dirty": "00", "command_mac": "/Applications/opera\\ 11.6.app/Contents/MacOS/Opera -newtab", "full_version": "11.6", "disk_space": "475 MB"}, {"version": "11", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 11\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1151_int_Setup.exe", "dirty": "00", "full_version": "11.0", "disk_space": "475 MB"}, {"version": "11.5", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 11\\opera.exe\"", "release": "stable", "dirty": "00", "full_version": "11.5", "disk_space": "475 MB"}, {"version": "12.15", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 12.15\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1215_int_Setup.exe", "dirty": "00", "command_mac": "/Applications/opera\\ 12.15.app/Contents/MacOS/Opera -newtab -pd /Users/<USER>/.operaprofile\\ 12.15", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_12.15_Setup_Intel.dmg", "full_version": "12.15", "disk_space": "475 MB"}, {"version": "12.16", "command": "start \"\" /MAX \"C:\\Program Files\\Opera 12.16\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_1216_int_Setup.exe", "dirty": "00", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_12.16_Setup_Intel.dmg", "full_version": "12.16", "disk_space": "475 MB"}, {"version": "15.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 15.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_15.0.1147.153_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 15.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_15.0.1147.153_Setup.dmg", "full_version": "15.0.1147.153", "disk_space": "100 MB"}, {"version": "16.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 16.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_16.0.1196.73_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 16.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_16.0.1196.73_Setup.dmg", "full_version": "16.0.1196.73", "disk_space": "100 MB"}, {"version": "17.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 17.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_17.0.1241.53_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 17.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_17.0.1241.53_Setup.dmg", "full_version": "17.0.1241.53", "disk_space": "100 MB"}, {"version": "18.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 18.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_18.0.1284.68_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 18.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_18.0.1284.68_Setup.dmg", "full_version": "18.0.1284.68", "disk_space": "100 MB"}, {"version": "19.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 19.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_19.0.1326.63_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 19.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_19.0.1326.63_Setup.dmg", "full_version": "19.0.1326.63", "disk_space": "100 MB"}, {"version": "20.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 20.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_20.0.1387.64_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 20.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_20.0.1387.64_Setup.dmg", "full_version": "20.0.1387.64", "disk_space": "100 MB"}, {"version": "21.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 21.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_21.0.1432.57_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 21.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_21.0.1432.57_Setup.dmg", "full_version": "21.0.1432.57", "disk_space": "100 MB"}, {"version": "22.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 22.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_22.0.1471.50_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 22.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_22.0.1471.50_Setup.dmg", "full_version": "22.0.1471.50", "disk_space": "100 MB"}, {"version": "23.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 23.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_23.0.1522.60_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 23.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_23.0.1522.60_Setup.dmg", "full_version": "23.0.1522.60", "disk_space": "100 MB"}, {"version": "24.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 24.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_24.0.1558.53_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 24.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_24.0.1558.53_Setup.dmg", "full_version": "24.0.1558.53", "disk_space": "100 MB"}, {"version": "25.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 25.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/Opera_25.0.1614.50_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 25.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/browserStack/mac/Opera_25.0.1614.50_Setup.dmg", "full_version": "25.0.1614.50", "disk_space": "125 MB"}, {"version": "26.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 26.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_26.0.1656.32_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 26.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_26.0.1656.32_Setup.dmg", "full_version": "26.0.1656.32", "disk_space": "135 MB"}, {"version": "27.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 27.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_27.0.1689.54_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 27.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_27.0.1689.54_Setup.dmg", "full_version": "27.0.1689.54", "disk_space": "135 MB"}, {"version": "28.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 28.0\\opera.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_28.0.1750.48_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 28.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_28.0.1750.48_Setup.dmg", "full_version": "28.0.1750.48", "disk_space": "135 MB"}, {"version": "29.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 29.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_29.0.1795.47_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 29.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_29.0.1795.47_Setup.dmg", "full_version": "29.0.1795.47", "disk_space": "130 MB"}, {"version": "30.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 30.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_30.0.1835.59_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 30.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_30.0.1835.59_Setup.dmg", "full_version": "30.0.1835.59", "disk_space": "130 MB"}, {"version": "31.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 31.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_31.0.1889.99_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 31.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_31.0.1889.99_Setup.dmg", "full_version": "31.0.1889.99", "disk_space": "130 MB"}, {"version": "32.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 32.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_32.0.1948.25_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 32.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_32.0.1948.25_Setup.dmg", "full_version": "32.0.1948.25", "disk_space": "130 MB"}, {"version": "33.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 33.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_33.0.1990.43_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 33.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_33.0.1990.43_Setup.dmg", "full_version": "33.0.1990.43", "disk_space": "130 MB"}, {"version": "34.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 34.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_34.0.2036.25_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 34.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_34.0.2036.25_Setup.dmg", "full_version": "34.0.2036.25", "disk_space": "135 MB"}, {"version": "35.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 35.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_35.0.2066.37_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 35.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_35.0.2066.37_Setup.dmg", "full_version": "35.0.2066.37", "disk_space": "135 MB"}, {"version": "36.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 36.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_36.0.2130.32_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 36.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_36.0.2130.32_Setup.dmg", "full_version": "36.0.2130.32", "disk_space": "140 MB"}, {"version": "37.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 37.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_37.0.2178.32_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 37.0.app/Contents/MacOS/Opera -newtab", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_37.0.2178.32_Setup.dmg", "full_version": "37.0.2178.32", "disk_space": "150 MB"}, {"version": "38.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 38.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/38.0.2220.29_opera_installer.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 38.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/38.0.2220.29_opera_installer.dmg", "full_version": "38.0.2220.29", "disk_space": "150 MB"}, {"version": "39.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 39.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/39.0.2256.43_opera_installer.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 39.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/39.0.2256.43_opera_installer.dmg", "full_version": "39.0.2256.43", "disk_space": "150 MB"}, {"version": "40.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 40.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/40.0.2308.54_opera_installer.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 40.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/40.0.2308.54_opera_installer.dmg", "full_version": "40.0.2308.54", "disk_space": "155 MB"}, {"version": "41.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 41.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/41.0.2353.46_opera_installer.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 41.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/41.0.2353.46_opera_installer.dmg", "full_version": "41.0.2353.46", "disk_space": "160 MB"}, {"version": "42.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 42.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/42.0.2393.85_opera_installer.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 42.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/42.0.2393.85_opera_installer.dmg", "full_version": "42.0.2393.85", "disk_space": "165 MB"}, {"version": "43.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 43.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_43.0.2442.806_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 43.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_43.0.2442.806_Setup.dmg", "full_version": "43.0.2442.806", "disk_space": "155 MB"}, {"version": "44.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 44.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_44.0.2510.857_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 44.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_44.0.2510.857_Setup.dmg", "full_version": "44.0.2510.857", "disk_space": "160 MB"}, {"version": "45.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 45.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_45.0.2552.635_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 45.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_45.0.2552.635_Setup.dmg", "full_version": "45.0.2552.635", "disk_space": "160 MB"}, {"version": "46.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 46.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_46.0.2597.26_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 46.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_46.0.2597.26_Setup.dmg", "full_version": "46.0.2597.26", "disk_space": "155 MB"}, {"version": "47.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 47.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_47.0.2631.48_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 47.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_47.0.2631.39_Setup.dmg", "full_version": "47.0.2631.48", "disk_space": "155 MB"}, {"version": "48.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 48.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_48.0.2685.32_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 48.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_48.0.2685.32_Setup.dmg", "full_version": "48.0.2685.32", "disk_space": "150 MB"}, {"version": "49.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 49.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_49.0.2725.34_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 49.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_49.0.2725.34_Setup.dmg", "full_version": "49.0.2725.34", "disk_space": "150 MB"}, {"version": "50.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 50.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_50.0.2762.45_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 50.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_50.0.2762.45_Setup.dmg", "full_version": "50.0.2762.45", "disk_space": "150 MB"}, {"version": "51.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 51.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_51.0.2830.26_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 51.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_51.0.2830.26_Setup.dmg", "full_version": "51.0.2830.26", "disk_space": "155 MB"}, {"version": "52.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 52.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_52.0.2871.30_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 52.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_52.0.2871.30_Setup.dmg", "full_version": "52.0.2871.30", "disk_space": "160 MB"}, {"version": "53.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 53.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_53.0.2907.37_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 53.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_53.0.2907.37_Setup.dmg", "full_version": "53.0.2907.37", "disk_space": "160 MB"}, {"version": "54.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 54.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_54.0.2952.41_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 54.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_54.0.2952.41_Setup.dmg", "full_version": "54.0.2952.41", "disk_space": "165 MB"}, {"version": "55.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 55.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_55.0.2994.37_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 55.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_55.0.2994.37_Setup.dmg", "full_version": "55.0.2994.37", "disk_space": "165 MB"}, {"version": "56.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 56.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_56.0.3051.31_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 56.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_56.0.3051.31_Setup.dmg", "full_version": "56.0.3051.31", "disk_space": "165 MB"}, {"version": "57.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 57.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_57.0.3098.76_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 57.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_57.0.3098.76_Setup.dmg", "full_version": "57.0.3098.76", "disk_space": "175 MB"}, {"version": "58.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 58.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_58.0.3135.47_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 58.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_58.0.3135.47_Setup.dmg", "full_version": "58.0.3135.47", "disk_space": "175 MB"}, {"version": "60.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 60.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_60.0.3255.27_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 60.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_60.0.3255.27_Setup.dmg", "full_version": "60.0.3255.27", "disk_space": "180 MB"}, {"version": "62.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 62.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_62.0.3331.18_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 62.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_62.0.3331.18_Setup.dmg", "full_version": "62.0.3331.18", "disk_space": "190 MB"}, {"version": "63.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 63.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_63.0.3368.35_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 63.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_63.0.3368.35_Setup.dmg", "full_version": "63.0.3368.35", "disk_space": "190 MB"}, {"version": "64.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 64.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_64.0.3417.47_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 64.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_64.0.3417.47_Setup.dmg", "full_version": "64.0.3417.47", "disk_space": "190 MB"}, {"version": "65.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 65.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_65.0.3467.38_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 65.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_65.0.3467.38_Setup.dmg", "full_version": "65.0.3467.38", "disk_space": "190 MB"}, {"version": "66.0", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 66.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_66.0.3515.27_Setup.exe", "dirty": "00", "command_mac": "/Applications/Opera\\ 66.0.app/Contents/MacOS/Opera -newtab --no-first-run", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_66.0.3515.27_Setup.dmg", "full_version": "66.0.3515.27", "disk_space": "190 MB"}, {"version": "67.0", "release": "stable", "dirty": "00", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_67.0.3575.31_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_67.0.3575.31_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 67.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 67.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "67.0.3575.31", "disk_space": "190 MB"}, {"version": "68.0", "release": "stable", "dirty": "00", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_68.0.3618.46_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_68.0.3618.46_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 68.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 68.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "68.0.3618.46", "disk_space": "190 MB"}, {"version": "120.0", "release": "dev", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_Developer_120.0.5530.0_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_Developer_120.0.5530.0_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera Developer 120.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ Developer\\ 120.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "120.0.5530.0", "disk_space": "480 MB"}, {"version": "119.0", "release": "dev", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_Developer_119.0.5495.0_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_Developer_119.0.5495.0_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera Developer 119.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ Developer\\ 119.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "119.0.5495.0", "disk_space": "480 MB"}, {"version": "69.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_69.0.3686.36_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_69.0.3686.36_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 69.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 69.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "69.0.3686.36", "disk_space": "190 MB"}, {"version": "70.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_70.0.3728.71_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_70.0.3728.71_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 70.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 70.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "70.0.3728.71", "disk_space": "190 MB"}, {"version": "71.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_71.0.3770.148_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_71.0.3770.148_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 71.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 71.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "71.0.3770.148", "disk_space": "190 MB"}, {"version": "72.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_72.0.3815.148_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_72.0.3815.148_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 72.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 72.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "72.0.3815.148", "disk_space": "195 MB"}, {"version": "73.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_73.0.3856.257_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_73.0.3856.257_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 73.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 73.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "73.0.3856.257", "disk_space": "200 MB"}, {"version": "74.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_74.0.3911.75_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_74.0.3911.75_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 74.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 74.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "74.0.3911.75", "disk_space": "200 MB"}, {"version": "75.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_75.0.3969.93_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_75.0.3969.93_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 75.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 75.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "75.0.3969.93", "disk_space": "205 MB"}, {"version": "76.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_76.0.4017.94_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_76.0.4017.94_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 76.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 76.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "76.0.4017.94", "disk_space": "375 MB"}, {"version": "77.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_77.0.4054.60_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_77.0.4054.60_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 77.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 77.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "77.0.4054.60", "disk_space": "375 MB"}, {"version": "78.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_78.0.4093.112_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_78.0.4093.112_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 78.0\\opera.exe\" --user-data-dir=\"C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\OperaStable29\" --no-first-run", "command_mac": "/Applications/Opera\\ 78.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "78.0.4093.112", "disk_space": "380 MB"}, {"version": "79.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_79.0.4143.22_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_79.0.4143.22_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 79.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 79.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "79.0.4143.22", "disk_space": "385 MB"}, {"version": "80.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_80.0.4170.16_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_80.0.4170.16_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 80.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 80.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "80.0.4170.16", "disk_space": "395 MB"}, {"version": "81.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_81.0.4196.31_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_81.0.4196.31_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 81.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 81.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "81.0.4196.31", "disk_space": "405 MB"}, {"version": "82.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_82.0.4227.23_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_82.0.4227.23_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 82.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 82.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "82.0.4227.23", "disk_space": "405 MB"}, {"version": "83.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_83.0.4254.19_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_83.0.4254.19_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 83.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 83.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "83.0.4254.19", "disk_space": "405 MB"}, {"version": "84.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_84.0.4316.14_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_84.0.4316.14_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 84.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 84.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "84.0.4316.14", "disk_space": "385 MB"}, {"version": "85.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_85.0.4341.18_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_85.0.4341.18_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 85.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 85.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "85.0.4341.18", "disk_space": "380 MB"}, {"version": "86.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_86.0.4363.23_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_86.0.4363.23_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 86.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 86.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "86.0.4363.23", "disk_space": "400 MB"}, {"version": "87.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_87.0.4390.25_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_87.0.4390.25_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 87.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 87.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "87.0.4390.25", "disk_space": "400 MB"}, {"version": "88.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_88.0.4412.27_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_88.0.4412.27_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 88.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 88.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "88.0.4412.27", "disk_space": "380 MB"}, {"version": "89.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_89.0.4447.38_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_89.0.4447.38_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 89.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 89.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "89.0.4447.38", "disk_space": "390 MB"}, {"version": "90.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_90.0.4480.48_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_90.0.4480.48_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 90.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 90.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "90.0.4480.48", "disk_space": "390 MB"}, {"version": "91.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_91.0.4516.16_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_91.0.4516.16_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 91.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 91.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "91.0.4516.16", "disk_space": "445 MB"}, {"version": "92.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_92.0.4561.21_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_92.0.4561.21_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 92.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 92.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "92.0.4561.21", "disk_space": "445 MB"}, {"version": "93.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_93.0.4585.11_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_93.0.4585.11_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 93.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 93.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "93.0.4585.11", "disk_space": "450 MB"}, {"version": "94.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_94.0.4606.26_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_94.0.4606.26_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 94.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 94.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "94.0.4606.26", "disk_space": "450 MB"}, {"version": "95.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_95.0.4635.25_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_95.0.4635.25_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 95.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 95.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "95.0.4635.25", "disk_space": "450 MB"}, {"version": "96.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_96.0.4693.20_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_96.0.4693.20_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 96.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 96.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "96.0.4693.20", "disk_space": "450 MB"}, {"version": "97.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_97.0.4719.26_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_97.0.4719.26_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 97.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 97.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "97.0.4719.26", "disk_space": "450 MB"}, {"version": "98.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_98.0.4759.6_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_98.0.4759.6_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 98.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 98.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "98.0.4759.6", "disk_space": "450 MB"}, {"version": "99.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_99.0.4788.9_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_99.0.4788.9_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 99.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 99.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "99.0.4788.9", "disk_space": "450 MB"}, {"version": "100.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_100.0.4815.20_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_100.0.4815.20_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 100.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 100.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "100.0.4815.20", "disk_space": "450 MB"}, {"version": "101.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_101.0.4843.25_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_101.0.4843.25_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 101.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 101.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "101.0.4843.25", "disk_space": "450 MB"}, {"version": "102.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_102.0.4880.16_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_102.0.4880.16_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 102.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 102.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "102.0.4880.16", "disk_space": "450 MB"}, {"version": "103.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_103.0.4928.16_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_103.0.4928.16_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 103.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 103.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "103.0.4928.16", "disk_space": "450 MB"}, {"version": "104.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_104.0.4944.23_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_104.0.4944.23_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 104.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 104.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "104.0.4944.23", "disk_space": "450 MB"}, {"version": "105.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_105.0.4970.13_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_105.0.4970.13_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 105.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 105.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "105.0.4970.13", "disk_space": "450 MB"}, {"version": "106.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_106.0.4998.16_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_106.0.4998.16_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 106.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 106.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "106.0.4998.16", "disk_space": "450 MB"}, {"version": "107.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_107.0.5045.15_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_107.0.5045.15_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 107.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 107.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "107.0.5045.15", "disk_space": "450 MB"}, {"version": "108.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_108.0.5067.20_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_108.0.5067.20_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 108.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 108.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "108.0.5067.20", "disk_space": "450 MB"}, {"version": "109.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_109.0.5097.33_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_109.0.5097.33_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 109.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 109.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "109.0.5097.33", "disk_space": "450 MB"}, {"version": "110.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_110.0.5130.23_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_110.0.5130.23_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 110.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 110.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "110.0.5130.23", "disk_space": "450 MB"}, {"version": "111.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_111.0.5168.25_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_111.0.5168.25_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 111.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 111.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "111.0.5168.25", "disk_space": "450 MB"}, {"version": "112.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_112.0.5197.24_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_112.0.5197.24_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 112.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 112.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "112.0.5197.24", "disk_space": "450 MB"}, {"version": "113.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_113.0.5230.31_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_113.0.5230.31_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 113.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 113.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "113.0.5230.31", "disk_space": "450 MB"}, {"version": "114.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_114.0.5282.21_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_114.0.5282.21_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 114.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 114.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "114.0.5282.21", "disk_space": "450 MB"}, {"version": "115.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_115.0.5322.68_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_115.0.5322.68_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 115.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 115.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "115.0.5322.68", "disk_space": "450 MB"}, {"version": "116.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_116.0.5366.21_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_116.0.5366.21_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 116.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 116.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "116.0.5366.21", "disk_space": "450 MB"}, {"version": "117.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_117.0.5408.35_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_117.0.5408.35_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 117.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 117.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "117.0.5408.35", "disk_space": "450 MB"}, {"version": "118.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_118.0.5461.41_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_118.0.5461.41_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 118.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 118.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "118.0.5461.41", "disk_space": "450 MB"}, {"version": "119.0", "release": "stable", "s3_path": "http://s3.amazonaws.com/bs-platform/windows/browsers/opera/Opera_119.0.5497.29_Setup.exe", "s3_mac_path": "http://s3.amazonaws.com/bs-platform/mac/browsers/opera/Opera_119.0.5497.29_Setup.dmg", "command": "start \"\" /MAX \"C:\\Program Files\\Opera\\Opera 119.0\\opera.exe\" --no-first-run", "command_mac": "/Applications/Opera\\ 119.0.app/Contents/MacOS/Opera -newtab --no-first-run", "full_version": "119.0.5497.29", "disk_space": "450 MB"}], "safari": [{"version": "4.0", "command": "start \"\" /MAX \"C:\\Program Files\\Safari4\\Safari.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/safari4/Safari.msi", "command_mac": "open -a \"/Applications/Safari 4.0.5.app\"", "full_version": "4.0", "disk_space": "50 MB"}, {"version": "5.0", "command": "start \"\" /MAX \"C:\\Program Files\\Safari5.0\\Safari.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/safari5/Safari.msi", "command_mac": "open -a \"/Applications/Safari5.app\"", "full_version": "5.0", "disk_space": "50 MB"}, {"version": "5.1", "command": "start \"\" /MAX \"C:\\Program Files\\Safari 5.1\\Safari.exe\"", "release": "stable", "s3_path": "http://s3.amazonaws.com/browserStack/softwares/safari5.1/Safari.msi", "command_mac": "open -a \"/Applications/Safari.app\"", "mac_os": "sl", "full_version": "5.1", "disk_space": "50 MB"}, {"version": "5.1", "command": "start \"\" /MAX \"C:\\Program Files\\Safari 5.1\\Safari.exe\"", "release": "stable", "command_mac": "open -a \"/Applications/Safari 5.1.5.app\"", "mac_os": "lion", "full_version": "5.1", "disk_space": "50 MB"}, {"version": "6.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "6.0", "disk_space": "50 MB"}, {"version": "6.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "6.1", "disk_space": "50 MB"}, {"version": "6.2", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "6.2", "disk_space": "50 MB"}, {"version": "7.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari_70.app\"", "full_version": "7.0", "disk_space": "50 MB"}, {"version": "7.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "7.1", "disk_space": "50 MB"}, {"version": "8.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "8.0", "disk_space": "50 MB"}, {"version": "9.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "9.0", "disk_space": "50 MB"}, {"version": "9.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "9.1", "disk_space": "50 MB"}, {"version": "10.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "10.0", "disk_space": "50 MB"}, {"version": "9.1.1", "release": "techpreview", "command_mac": "open -a \"/Applications/Safari Technology Preview.app/Contents/MacOS/Safari Technology Preview\"", "full_version": "9.1.1", "disk_space": "50 MB"}, {"version": "10.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "10.1", "disk_space": "50 MB"}, {"version": "11.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "11.0", "disk_space": "50 MB"}, {"version": "11.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "11.1", "disk_space": "50 MB"}, {"version": "12.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "12.0", "disk_space": "50 MB"}, {"version": "12.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "12.1", "disk_space": "50 MB"}, {"version": "13.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "13.0", "disk_space": "50 MB"}, {"version": "13.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "13.1", "disk_space": "50 MB"}, {"version": "14.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "14.0", "disk_space": "50 MB"}, {"version": "14.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "14.1", "disk_space": "50 MB"}, {"version": "15.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "15.0", "disk_space": "50 MB"}, {"version": "15.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "15.1", "disk_space": "50 MB"}, {"version": "15.3", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "15.3", "disk_space": "50 MB"}, {"version": "15.6", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "15.6", "disk_space": "50 MB"}, {"version": "16.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "16.0", "disk_space": "50 MB"}, {"version": "16.3", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "16.3", "disk_space": "50 MB"}, {"version": "16.5", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "16.5", "disk_space": "50 MB"}, {"version": "17.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "17.0", "disk_space": "50 MB"}, {"version": "17.3", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "17.3", "disk_space": "50 MB"}, {"version": "18.0", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "18.0", "disk_space": "50 MB"}, {"version": "18.1", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "18.1", "disk_space": "50 MB"}, {"version": "18.4", "release": "stable", "command_mac": "open -a \"/Applications/Safari.app\"", "full_version": "18.4", "disk_space": "50 MB"}]}