/**
 * Example usage of the Device Log Handler
 * 
 * This example demonstrates how to use the custom device log functionality
 * for logcat and syslog types through the Selenium WebDriver endpoint.
 */

'use strict';

// Example of how to enable device logs in session capabilities
const exampleCapabilities = {
  desiredCapabilities: {
    device: 'Samsung Galaxy S21',
    os_version: '11.0',
    'browserstack.deviceLogs': true  // Enable device logs
  }
};

// Example of how a client would make requests to get device logs

const exampleLogcatRequest = {
  method: 'POST',
  url: '/wd/hub/session/test-session-id/log',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    type: 'logcat'
  })
};

const exampleSyslogRequest = {
  method: 'POST',
  url: '/wd/hub/session/test-session-id/log',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    type: 'syslog'
  })
};

// Example response format
const exampleSuccessResponse = {
  sessionId: 'test-session-id',
  status: 0,
  value: [
    {
      timestamp: 1748599327715,
      level: 'ALL',
      message: '--------- beginning of main'
    },
    {
      timestamp: 1748599327716,
      level: 'INFO',
      message: 'Application started'
    }
  ]
};

// Example error response when device logs are not enabled
const exampleErrorResponse = {
  sessionId: 'test-session-id',
  status: 13,
  value: {
    message: 'Device logs must be enabled for this session'
  }
};

// Example of how the platform endpoint is called internally
const examplePlatformRequest = {
  hostname: 'rproxy-host',
  port: 45671,
  path: '/device_logs?device=test-device&session_id=test-session-id&log_type=logcat&start_pos=0',
  headers: {
    'Host': 'terminal-name'
  }
};

// Example platform response
const examplePlatformResponse = {
  statusCode: 200,
  data: JSON.stringify({
    meta: {
      start_pos: 1234654,
      end_pos: 1239009
    },
    value: [
      {
        timestamp: 1748599327715,
        level: 'ALL',
        message: '--------- beginning of main'
      }
    ]
  })
};

console.log('Device Log Handler Examples:');
console.log('============================');
console.log('\n1. Session Capabilities:', JSON.stringify(exampleCapabilities, null, 2));
console.log('\n2. Logcat Request:', JSON.stringify(exampleLogcatRequest, null, 2));
console.log('\n3. Syslog Request:', JSON.stringify(exampleSyslogRequest, null, 2));
console.log('\n4. Success Response:', JSON.stringify(exampleSuccessResponse, null, 2));
console.log('\n5. Error Response:', JSON.stringify(exampleErrorResponse, null, 2));
console.log('\n6. Platform Request:', JSON.stringify(examplePlatformRequest, null, 2));
console.log('\n7. Platform Response:', JSON.stringify(examplePlatformResponse, null, 2));

/**
 * Key Features Implemented:
 * 
 * 1. Request Interception:
 *    - Intercepts POST requests to /wd/hub/session/<session-id>/log
 *    - Checks if request type is "logcat" or "syslog"
 *    - Routes to custom handler if match, otherwise continues normal flow
 * 
 * 2. Session Validation:
 *    - Checks if deviceLogs is true in session (mapped from browserstack.deviceLogs capability)
 *    - Returns error if device logs are not enabled
 * 
 * 3. Platform Integration:
 *    - Calls /device_logs endpoint on platform
 *    - Passes device, session_id, log_type, and start_pos parameters
 * 
 * 4. Response Processing:
 *    - Stores end_pos for pagination in subsequent requests
 *    - Removes meta object from response
 *    - Returns only the value array to client
 * 
 * 5. Error Handling:
 *    - Validates request data format
 *    - Handles platform errors gracefully
 *    - Returns appropriate error messages
 */
