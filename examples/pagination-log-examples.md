# Device Log Handler - Pagination Logging Examples

## Overview

This document shows examples of the pagination debug logs that will be generated by the <PERSON>ceLogHandler when processing device log requests.

## Example Log Sequence

### First Request (Initial State)

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: true, appiumLogs: false, consoleLogsEnabled: true
[DEBUG] [DeviceLogHandler] Pagination - Retrieved deviceLogEndPos from session: 0 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Making request to platform with start_pos=0: /device_logs?device=Samsung%20Galaxy%20S21&session_id=abc123-session-id&log_type=logcat&start_pos=0 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Response from platform: {"statusCode":200,"data":"{\"meta\":{\"start_pos\":0,\"end_pos\":1500},\"value\":[{\"timestamp\":1748599327715,\"level\":\"INFO\",\"message\":\"App started\"},{\"timestamp\":1748599327716,\"level\":\"DEBUG\",\"message\":\"Loading configuration\"}]}"}
[DEBUG] [DeviceLogHandler] Pagination - Platform response parsed successfully for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Received new end_pos from platform: 1500 (previous: 0) for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Successfully updated deviceLogEndPos in global registry to: 1500 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Returning 2 log entries to client for session: abc123-session-id. Current deviceLogEndPos: 1500
```

### Second Request (Using Stored Position)

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: true, appiumLogs: false, consoleLogsEnabled: true
[DEBUG] [DeviceLogHandler] Pagination - Retrieved deviceLogEndPos from session: 1500 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Making request to platform with start_pos=1500: /device_logs?device=Samsung%20Galaxy%20S21&session_id=abc123-session-id&log_type=logcat&start_pos=1500 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Response from platform: {"statusCode":200,"data":"{\"meta\":{\"start_pos\":1500,\"end_pos\":3200},\"value\":[{\"timestamp\":1748599329000,\"level\":\"WARN\",\"message\":\"Network timeout\"},{\"timestamp\":1748599329500,\"level\":\"ERROR\",\"message\":\"Connection failed\"}]}"}
[DEBUG] [DeviceLogHandler] Pagination - Platform response parsed successfully for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Received new end_pos from platform: 3200 (previous: 1500) for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Successfully updated deviceLogEndPos in global registry to: 3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Returning 2 log entries to client for session: abc123-session-id. Current deviceLogEndPos: 3200
```

### Third Request (No New Logs)

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: true, appiumLogs: false, consoleLogsEnabled: true
[DEBUG] [DeviceLogHandler] Pagination - Retrieved deviceLogEndPos from session: 3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Making request to platform with start_pos=3200: /device_logs?device=Samsung%20Galaxy%20S21&session_id=abc123-session-id&log_type=syslog&start_pos=3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Response from platform: {"statusCode":200,"data":"{\"meta\":{\"start_pos\":3200,\"end_pos\":3200},\"value\":[]}"}
[DEBUG] [DeviceLogHandler] Pagination - Platform response parsed successfully for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Received new end_pos from platform: 3200 (previous: 3200) for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Successfully updated deviceLogEndPos in global registry to: 3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Returning 0 log entries to client for session: abc123-session-id. Current deviceLogEndPos: 3200
```

## Error Scenarios

### Platform Request Failure

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: true, appiumLogs: false, consoleLogsEnabled: true
[DEBUG] [DeviceLogHandler] Pagination - Retrieved deviceLogEndPos from session: 3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Making request to platform with start_pos=3200: /device_logs?device=Samsung%20Galaxy%20S21&session_id=abc123-session-id&log_type=logcat&start_pos=3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Response from platform: {"statusCode":500,"data":"Internal Server Error"}
[WARN] [DeviceLogHandler] Pagination - Platform request failed with status 500 for session: abc123-session-id. Current deviceLogEndPos: 3200
```

### JSON Parsing Error

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: true, appiumLogs: false, consoleLogsEnabled: true
[DEBUG] [DeviceLogHandler] Pagination - Retrieved deviceLogEndPos from session: 3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Making request to platform with start_pos=3200: /device_logs?device=Samsung%20Galaxy%20S21&session_id=abc123-session-id&log_type=logcat&start_pos=3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Response from platform: {"statusCode":200,"data":"Invalid JSON response"}
[WARN] [DeviceLogHandler] Pagination - Failed to parse platform response for session: abc123-session-id. Error: Unexpected token I in JSON at position 0
[WARN] [DeviceLogHandler] Pagination - Exception occurred for session: abc123-session-id. Current deviceLogEndPos: 3200. Error: Invalid JSON response from platform
```

### Missing end_pos in Response

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: true, appiumLogs: false, consoleLogsEnabled: true
[DEBUG] [DeviceLogHandler] Pagination - Retrieved deviceLogEndPos from session: 3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Making request to platform with start_pos=3200: /device_logs?device=Samsung%20Galaxy%20S21&session_id=abc123-session-id&log_type=logcat&start_pos=3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Response from platform: {"statusCode":200,"data":"{\"value\":[{\"timestamp\":1748599330000,\"level\":\"INFO\",\"message\":\"Log without meta\"}]}"}
[DEBUG] [DeviceLogHandler] Pagination - Platform response parsed successfully for session: abc123-session-id
[WARN] [DeviceLogHandler] Pagination - No end_pos found in platform response meta for session: abc123-session-id. Response meta: undefined
[DEBUG] [DeviceLogHandler] Pagination - Returning 1 log entries to client for session: abc123-session-id. Current deviceLogEndPos: 3200
```

### Session Not in Global Registry

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: true, appiumLogs: false, consoleLogsEnabled: true
[DEBUG] [DeviceLogHandler] Pagination - Retrieved deviceLogEndPos from session: 3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Making request to platform with start_pos=3200: /device_logs?device=Samsung%20Galaxy%20S21&session_id=abc123-session-id&log_type=logcat&start_pos=3200 for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Response from platform: {"statusCode":200,"data":"{\"meta\":{\"start_pos\":3200,\"end_pos\":4800},\"value\":[{\"timestamp\":1748599331000,\"level\":\"INFO\",\"message\":\"New log entry\"}]}"}
[DEBUG] [DeviceLogHandler] Pagination - Platform response parsed successfully for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Received new end_pos from platform: 4800 (previous: 3200) for session: abc123-session-id
[WARN] [DeviceLogHandler] Pagination - WARNING: Session not found in global registry, deviceLogEndPos may not persist for session: abc123-session-id
[DEBUG] [DeviceLogHandler] Pagination - Returning 1 log entries to client for session: abc123-session-id. Current deviceLogEndPos: 4800
```

## Device Logs Disabled

```
[DEBUG] [DeviceLogHandler] Session properties - deviceLogs: false, appiumLogs: false, consoleLogsEnabled: true
[WARN] [DeviceLogHandler] Device logs not enabled for session: abc123-session-id. deviceLogs property: false
```

## Log Analysis Tips

### Successful Pagination Flow
Look for this pattern:
1. `Retrieved deviceLogEndPos from session: X`
2. `Making request to platform with start_pos=X`
3. `Received new end_pos from platform: Y (previous: X)`
4. `Successfully updated deviceLogEndPos in global registry to: Y`
5. `Returning N log entries to client`

### Debugging Issues

**Pagination Not Working:**
- Check if `deviceLogEndPos` values are increasing
- Verify `start_pos` matches previous `end_pos`
- Look for registry update warnings

**Duplicate Logs:**
- Check if `start_pos` is advancing correctly
- Verify platform is returning correct `end_pos`

**Missing Logs:**
- Look for gaps in `end_pos` progression
- Check for platform errors or parsing failures

**Performance Issues:**
- Monitor log entry counts
- Check frequency of requests
- Look for large `end_pos` jumps

### Log Filtering

Use these patterns to filter logs:
- `grep "Pagination -"` - All pagination logs
- `grep "deviceLogEndPos"` - Pagination state changes
- `grep "start_pos="` - Platform requests
- `grep "end_pos"` - Platform responses
- `grep "\[WARN\].*Pagination"` - Pagination warnings
