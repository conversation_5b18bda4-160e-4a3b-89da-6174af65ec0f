# Device Log Handler Implementation

## Overview

This implementation adds custom log handling functionality for the Selenium WebDriver endpoint `/wd/hub/session/<selenium-session-id>/log` to support device logs (logcat and syslog types).

## Implementation Details

### Files Modified/Created

1. **hub.js** - Added request interception logic
2. **controllers/seleniumCommand/handlers/DeviceLogHandler.js** - New custom log handler
3. **test/unit/controllers/seleniumCommand/handlers/DeviceLogHandler.test.js** - Unit tests
4. **examples/device-log-usage.js** - Usage examples

### Key Features

#### 1. Request Interception
- Intercepts POST requests to `/wd/hub/session/<session-id>/log`
- Checks if the request type is "logcat" or "syslog"
- Routes to custom handler if match, otherwise continues with normal flow (e.g., Firefox console logs)

#### 2. Session Validation
- Validates that `deviceLogs` is true in the session (mapped from `browserstack.deviceLogs` capability)
- Returns error message if device logs are not enabled: "Device logs must be enabled for this session"

#### 3. Custom Log Handler
- Located at `controllers/seleniumCommand/handlers/DeviceLogHandler.js`
- Follows the same pattern as other handlers like `IOSLocationHandler.js`
- Validates request data format
- Calls the platform `/device_logs` endpoint

#### 4. Platform Integration
- Makes HTTP request to `/device_logs` endpoint on port 45671
- Passes required parameters:
  - `device`: The device identifier
  - `session_id`: The session identifier
  - `log_type`: Either "logcat" or "syslog"
  - `start_pos`: Position for pagination (stored from previous requests)

#### 5. Response Processing
- Stores `end_pos` from platform response metadata for pagination
- Removes the `meta` object from response before returning to user
- Returns only the `value` array containing actual log entries

#### 6. Error Handling
- Validates JSON format of request data
- Handles platform errors gracefully
- Returns appropriate error messages with status codes

## Usage

### Client Request Format

```javascript
// Logcat request
POST /wd/hub/session/<session-id>/log
Content-Type: application/json

{
  "type": "logcat"
}

// Syslog request
POST /wd/hub/session/<session-id>/log
Content-Type: application/json

{
  "type": "syslog"
}
```

### Response Format

#### Success Response
```javascript
{
  "sessionId": "session-id",
  "status": 0,
  "value": [
    {
      "timestamp": 1748599327715,
      "level": "ALL",
      "message": "--------- beginning of main"
    }
  ]
}
```

#### Error Response
```javascript
{
  "sessionId": "session-id",
  "status": 13,
  "value": {
    "message": "Device logs must be enabled for this session"
  }
}
```

## Platform Endpoint

The handler calls the platform endpoint with the following format:

```
GET /device_logs?device=<device>&session_id=<session-id>&log_type=<type>&start_pos=<position>
Host: <terminal-name>
```

### Expected Platform Response

```javascript
{
  "meta": {
    "start_pos": 1234654,
    "end_pos": 1239009
  },
  "value": [
    {
      "timestamp": 1748599327715,
      "level": "ALL",
      "message": "--------- beginning of main"
    }
  ]
}
```

## Pagination

The implementation supports pagination through the `start_pos` parameter:
- Initial requests use `start_pos=0`
- Subsequent requests use the `end_pos` value from the previous response
- The `end_pos` is stored in the session object as `deviceLogEndPos`

## Testing

Unit tests are provided in `test/unit/controllers/seleniumCommand/handlers/DeviceLogHandler.test.js` covering:
- Request validation
- Session validation
- Successful log retrieval
- Error handling
- Pagination functionality

## Integration

The implementation integrates seamlessly with the existing codebase:
- Follows established patterns from other handlers
- Uses existing infrastructure (bridge, requestlib, HubLogger)
- Maintains backward compatibility with existing log functionality
- Does not affect other log types (browser, driver, etc.)

## Configuration

To enable device logs for a session, set the capability during session creation:
```javascript
{
  "desiredCapabilities": {
    "browserstack.deviceLogs": true
    // or "browserstack.deviceLogs": "true"
  }
}
```

This capability is automatically mapped to the session object property `deviceLogs` during session creation.
