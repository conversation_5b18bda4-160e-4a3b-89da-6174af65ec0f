/**
 * Integration test to verify device log validation fix
 * 
 * This test verifies that:
 * 1. The browserstack.deviceLogs capability is correctly mapped to session.deviceLogs
 * 2. The DeviceLogHandler correctly validates the deviceLogs property
 * 3. Both enabled and disabled scenarios work as expected
 */

'use strict';

const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } = require('../../controllers/seleniumCommand/handlers/DeviceLogHandler');

// Mock session objects to test different scenarios
const createMockSession = (deviceLogsEnabled) => ({
  rails_session_id: 'test-session-id',
  device: 'test-device',
  name: 'test-terminal',
  rproxyHost: 'test-rproxy-host',
  idle_timeout: 300,
  deviceLogs: deviceLogsEnabled,  // This is the correct property name
  deviceLogEndPos: 0,
  debugSession: false
});

// Mock request state object
const createMockRequestState = () => ({
  data: null,
  output: null
});

// Mock bridge.sendResponse to capture the response
let capturedResponse = null;
const mockBridge = {
  sendResponse: (sessionKeyObj, requestStateObj) => {
    capturedResponse = {
      sessionKeyObj,
      requestStateObj
    };
  }
};

// Mock requestlib.call to simulate platform response
let mockPlatformResponse = null;
const mockRequestlib = {
  call: async () => mockPlatformResponse,
  appendBStackHostHeader: (name) => ({ 'Host': name })
};

// Mock HubLogger
const mockHubLogger = {
  miscLogger: (component, message, level, debugSession) => {
    console.log(`[${component}] ${message}`);
  },
  exceptionLogger: (message) => {
    console.log(`[EXCEPTION] ${message}`);
  }
};

// Mock constants
const mockConstants = {
  LOG_LEVEL: { DEBUG: 'DEBUG', WARN: 'WARN' },
  CHUNKED_HEADER: { 'Transfer-Encoding': 'chunked' }
};

// Replace the actual modules with mocks
const originalBridge = require('../../bridge');
const originalRequestlib = require('../../lib/request');
const originalHubLogger = require('../../log');
const originalConstants = require('../../constants');

// Test function
async function runValidationTests() {
  console.log('Running Device Log Validation Tests...\n');

  // Test 1: Device logs enabled - should allow log retrieval
  console.log('Test 1: Device logs enabled');
  try {
    const sessionWithLogsEnabled = createMockSession(true);
    const handler = new DeviceLogHandler(sessionWithLogsEnabled, {}, {});
    const requestStateObj = createMockRequestState();
    const logData = JSON.stringify({ type: 'logcat' });

    // Mock successful platform response
    mockPlatformResponse = {
      statusCode: 200,
      data: JSON.stringify({
        meta: { start_pos: 0, end_pos: 1000 },
        value: [
          { timestamp: Date.now(), level: 'INFO', message: 'Test log entry' }
        ]
      })
    };

    // Temporarily replace modules
    require.cache[require.resolve('../../bridge')].exports = mockBridge;
    require.cache[require.resolve('../../lib/request')].exports = mockRequestlib;
    require.cache[require.resolve('../../log')].exports = mockHubLogger;
    require.cache[require.resolve('../../constants')].exports = mockConstants;

    await handler.processCommand(requestStateObj, logData);

    if (capturedResponse && capturedResponse.requestStateObj.data) {
      const response = JSON.parse(capturedResponse.requestStateObj.data);
      if (response.status === 0) {
        console.log('✅ PASS: Device logs enabled - log retrieval successful');
      } else {
        console.log('❌ FAIL: Device logs enabled but got error response:', response);
      }
    } else {
      console.log('❌ FAIL: No response captured for enabled device logs test');
    }
  } catch (error) {
    console.log('❌ FAIL: Exception in enabled device logs test:', error.message);
  }

  // Reset captured response
  capturedResponse = null;

  // Test 2: Device logs disabled - should return error
  console.log('\nTest 2: Device logs disabled');
  try {
    const sessionWithLogsDisabled = createMockSession(false);
    const handler = new DeviceLogHandler(sessionWithLogsDisabled, {}, {});
    const requestStateObj = createMockRequestState();
    const logData = JSON.stringify({ type: 'logcat' });

    await handler.processCommand(requestStateObj, logData);

    if (capturedResponse && capturedResponse.requestStateObj.data) {
      const response = JSON.parse(capturedResponse.requestStateObj.data);
      if (response.status === 13 && response.value.message.includes('Device logs must be enabled')) {
        console.log('✅ PASS: Device logs disabled - correct error returned');
      } else {
        console.log('❌ FAIL: Device logs disabled but got unexpected response:', response);
      }
    } else {
      console.log('❌ FAIL: No response captured for disabled device logs test');
    }
  } catch (error) {
    console.log('❌ FAIL: Exception in disabled device logs test:', error.message);
  }

  // Test 3: Undefined device logs property - should return error
  console.log('\nTest 3: Undefined device logs property');
  try {
    const sessionWithUndefinedLogs = createMockSession(undefined);
    const handler = new DeviceLogHandler(sessionWithUndefinedLogs, {}, {});
    const requestStateObj = createMockRequestState();
    const logData = JSON.stringify({ type: 'syslog' });

    await handler.processCommand(requestStateObj, logData);

    if (capturedResponse && capturedResponse.requestStateObj.data) {
      const response = JSON.parse(capturedResponse.requestStateObj.data);
      if (response.status === 13 && response.value.message.includes('Device logs must be enabled')) {
        console.log('✅ PASS: Undefined device logs - correct error returned');
      } else {
        console.log('❌ FAIL: Undefined device logs but got unexpected response:', response);
      }
    } else {
      console.log('❌ FAIL: No response captured for undefined device logs test');
    }
  } catch (error) {
    console.log('❌ FAIL: Exception in undefined device logs test:', error.message);
  }

  // Restore original modules
  require.cache[require.resolve('../../bridge')].exports = originalBridge;
  require.cache[require.resolve('../../lib/request')].exports = originalRequestlib;
  require.cache[require.resolve('../../log')].exports = originalHubLogger;
  require.cache[require.resolve('../../constants')].exports = originalConstants;

  console.log('\nDevice Log Validation Tests Complete!');
}

// Run the tests
if (require.main === module) {
  runValidationTests().catch(console.error);
}

module.exports = { runValidationTests };
