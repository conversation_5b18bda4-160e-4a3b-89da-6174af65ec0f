/**
 * Demonstration of pagination logging in <PERSON>ceLogHandler
 * 
 * This script simulates multiple device log requests to show how pagination
 * logging works and helps debug pagination issues.
 */

'use strict';

// Mock the dependencies to capture log output
const mockLogs = [];

const mockHubLogger = {
  miscLogger: (component, message, level, debugSession) => {
    const logEntry = `[${level}] [${component}] ${message}`;
    mockLogs.push(logEntry);
    console.log(logEntry);
  },
  exceptionLogger: (message) => {
    const logEntry = `[EXCEPTION] ${message}`;
    mockLogs.push(logEntry);
    console.log(logEntry);
  }
};

const mockConstants = {
  LOG_LEVEL: { DEBUG: 'DEBUG', WARN: 'WARN' },
  CHUNKED_HEADER: { 'Transfer-Encoding': 'chunked' },
  global_registry: {}
};

const mockBridge = {
  sendResponse: (sessionKeyObj, requestStateObj) => {
    console.log(`[BRIDGE] Response sent for session: ${sessionKeyObj.rails_session_id}`);
  }
};

const mockRequestlib = {
  call: async (options) => {
    // Simulate different platform responses for demonstration
    return mockPlatformResponse;
  },
  appendBStackHostHeader: (name) => ({ 'Host': name })
};

// Mock platform responses for different scenarios
let mockPlatformResponse = null;

// Temporarily replace modules for demonstration
const originalModules = {};
function mockModules() {
  originalModules.HubLogger = require.cache[require.resolve('../log')];
  originalModules.constants = require.cache[require.resolve('../constants')];
  originalModules.bridge = require.cache[require.resolve('../bridge')];
  originalModules.requestlib = require.cache[require.resolve('../lib/request')];

  require.cache[require.resolve('../log')] = { exports: mockHubLogger };
  require.cache[require.resolve('../constants')] = { exports: mockConstants };
  require.cache[require.resolve('../bridge')] = { exports: mockBridge };
  require.cache[require.resolve('../lib/request')] = { exports: mockRequestlib };
}

function restoreModules() {
  if (originalModules.HubLogger) require.cache[require.resolve('../log')] = originalModules.HubLogger;
  if (originalModules.constants) require.cache[require.resolve('../constants')] = originalModules.constants;
  if (originalModules.bridge) require.cache[require.resolve('../bridge')] = originalModules.bridge;
  if (originalModules.requestlib) require.cache[require.resolve('../lib/request')] = originalModules.requestlib;
}

async function demonstratePaginationLogging() {
  console.log('='.repeat(80));
  console.log('DEVICE LOG HANDLER - PAGINATION LOGGING DEMONSTRATION');
  console.log('='.repeat(80));

  try {
    // Mock modules
    mockModules();

    // Import DeviceLogHandler after mocking
    const { DeviceLogHandler } = require('../controllers/seleniumCommand/handlers/DeviceLogHandler');

    // Create a mock session
    const sessionKeyObj = {
      rails_session_id: 'demo-session-123',
      device: 'Samsung Galaxy S21',
      name: 'demo-terminal',
      rproxyHost: 'demo-rproxy-host',
      idle_timeout: 300,
      deviceLogs: true,
      debugSession: true  // Enable debug logging
    };

    // Add session to global registry
    mockConstants.global_registry[sessionKeyObj.rails_session_id] = sessionKeyObj;

    console.log('\n📱 Scenario 1: First request (no previous deviceLogEndPos)');
    console.log('-'.repeat(60));

    // First request - no previous deviceLogEndPos
    mockPlatformResponse = {
      statusCode: 200,
      data: JSON.stringify({
        meta: {
          start_pos: 0,
          end_pos: 1500
        },
        value: [
          { timestamp: Date.now(), level: 'INFO', message: 'First log entry' },
          { timestamp: Date.now() + 1000, level: 'DEBUG', message: 'Second log entry' }
        ]
      })
    };

    const handler1 = new DeviceLogHandler(sessionKeyObj, {}, {});
    const requestStateObj1 = {};
    const logData1 = JSON.stringify({ type: 'logcat' });

    await handler1.processCommand(requestStateObj1, logData1);

    console.log('\n📱 Scenario 2: Second request (using stored deviceLogEndPos)');
    console.log('-'.repeat(60));

    // Second request - should use stored deviceLogEndPos
    mockPlatformResponse = {
      statusCode: 200,
      data: JSON.stringify({
        meta: {
          start_pos: 1500,
          end_pos: 3000
        },
        value: [
          { timestamp: Date.now() + 2000, level: 'WARN', message: 'Third log entry' },
          { timestamp: Date.now() + 3000, level: 'ERROR', message: 'Fourth log entry' }
        ]
      })
    };

    const handler2 = new DeviceLogHandler(sessionKeyObj, {}, {});
    const requestStateObj2 = {};
    const logData2 = JSON.stringify({ type: 'syslog' });

    await handler2.processCommand(requestStateObj2, logData2);

    console.log('\n📱 Scenario 3: Platform error (pagination state preserved)');
    console.log('-'.repeat(60));

    // Third request - platform error
    mockPlatformResponse = {
      statusCode: 500,
      data: 'Internal Server Error'
    };

    const handler3 = new DeviceLogHandler(sessionKeyObj, {}, {});
    const requestStateObj3 = {};
    const logData3 = JSON.stringify({ type: 'logcat' });

    await handler3.processCommand(requestStateObj3, logData3);

    console.log('\n📱 Scenario 4: Response without meta (edge case)');
    console.log('-'.repeat(60));

    // Fourth request - response without meta
    mockPlatformResponse = {
      statusCode: 200,
      data: JSON.stringify({
        value: [
          { timestamp: Date.now() + 4000, level: 'INFO', message: 'Fifth log entry' }
        ]
      })
    };

    const handler4 = new DeviceLogHandler(sessionKeyObj, {}, {});
    const requestStateObj4 = {};
    const logData4 = JSON.stringify({ type: 'logcat' });

    await handler4.processCommand(requestStateObj4, logData4);

    console.log('\n📱 Scenario 5: Session not in global registry (warning case)');
    console.log('-'.repeat(60));

    // Remove session from global registry to test warning
    delete mockConstants.global_registry[sessionKeyObj.rails_session_id];

    mockPlatformResponse = {
      statusCode: 200,
      data: JSON.stringify({
        meta: {
          start_pos: 3000,
          end_pos: 4500
        },
        value: [
          { timestamp: Date.now() + 5000, level: 'INFO', message: 'Sixth log entry' }
        ]
      })
    };

    const handler5 = new DeviceLogHandler(sessionKeyObj, {}, {});
    const requestStateObj5 = {};
    const logData5 = JSON.stringify({ type: 'syslog' });

    await handler5.processCommand(requestStateObj5, logData5);

  } catch (error) {
    console.error('Error in demonstration:', error);
  } finally {
    // Restore original modules
    restoreModules();
  }

  console.log('\n' + '='.repeat(80));
  console.log('PAGINATION LOGGING SUMMARY');
  console.log('='.repeat(80));

  console.log('\n🔍 Key Logging Points Demonstrated:');
  console.log('1. ✅ Retrieved deviceLogEndPos from session at request start');
  console.log('2. ✅ Platform request with start_pos parameter');
  console.log('3. ✅ Platform response parsing status');
  console.log('4. ✅ New end_pos value received from platform');
  console.log('5. ✅ deviceLogEndPos update in session and global registry');
  console.log('6. ✅ Log entries count returned to client');
  console.log('7. ✅ Error cases with pagination state information');
  console.log('8. ✅ Warning when session not found in global registry');

  console.log('\n📊 Log Analysis:');
  const debugLogs = mockLogs.filter(log => log.includes('[DEBUG]')).length;
  const warnLogs = mockLogs.filter(log => log.includes('[WARN]')).length;
  const paginationLogs = mockLogs.filter(log => log.includes('Pagination')).length;

  console.log(`- Total logs generated: ${mockLogs.length}`);
  console.log(`- Debug logs: ${debugLogs}`);
  console.log(`- Warning logs: ${warnLogs}`);
  console.log(`- Pagination-specific logs: ${paginationLogs}`);

  console.log('\n💡 These logs will help debug:');
  console.log('- Pagination state persistence across requests');
  console.log('- Platform response parsing issues');
  console.log('- Session registry synchronization problems');
  console.log('- start_pos/end_pos value tracking');
  console.log('- Log entry count validation');
}

// Run the demonstration
if (require.main === module) {
  demonstratePaginationLogging().catch(console.error);
}

module.exports = { demonstratePaginationLogging };
